using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class ContainerInventoryUI : Mono<PERSON>ehaviour, IPointerClickHandler
{
    [Header("References")]
    public ContainerInventoryManager containerManager;
    public GameObject slotPrefab;
    public GameObject itemUIPrefab;
    public Transform slotContainer;
    public Transform itemContainer;
    public Vector2 slotSize = new Vector2();
    
    [Header("Visual Effects")]
    [SerializeField] private Material retroMaterial;
    
    [Header("Grid Highlights")]
    [SerializeField] private Color validPlacementColor = new Color(0.3840584f, 0.6301887f, 0.47836998f, 0.2f);
    [SerializeField] private Color invalidPlacementColor = new Color(0.5999999f, 0.08037735f, 0.08037735f, 0.29411766f);
    
    // Static instance for container access
    public static ContainerInventoryUI Instance { get; private set; }
    
    // Dedicated highlight system
    private GameObject highlightContainer;
    private GameObject[] cellHighlights;
    private bool highlightSystemInitialized = false;
    
    // Properties for external access
    public Vector2 GridOffset => gridOffset;
    public RectTransform GridRectTransform => gridRectTransform;
    public Vector2 GridSpacing => gridLayout != null ? gridLayout.spacing : Vector2.zero;
    
    // Private members
    private GridLayoutGroup gridLayout;
    private RectTransform gridRectTransform;
    private Vector2 gridOffset;
    private Dictionary<int, InventoryItemUI> itemUIMapping = new Dictionary<int, InventoryItemUI>();
    
    // Animation system
    private RectTransform rectTransform;
    private CanvasGroup canvasGroup;
    private Vector2 initialAnchoredPosition;
    private Vector2 closedOffset = new Vector2(800f, 0f); // Slide in from right
    private Coroutine animationCoroutine;
    
    [Header("Animation Settings")]
    [SerializeField] private float animationDuration = 0.3f;
    [SerializeField] private AnimationCurve animationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    
    private void Awake()
    {
        // Set up singleton
        if (Instance == null)
            Instance = this;
        else
            Destroy(gameObject);
        
        // Get components
        rectTransform = GetComponent<RectTransform>();
        canvasGroup = GetComponent<CanvasGroup>();
        if (canvasGroup == null)
            canvasGroup = gameObject.AddComponent<CanvasGroup>();
        
        // Store the initial position set in the editor
        initialAnchoredPosition = rectTransform.anchoredPosition;
        // Start hidden (offscreen)
        rectTransform.anchoredPosition = initialAnchoredPosition + closedOffset;
        canvasGroup.alpha = 0f;
        
        // Ensure we have the necessary components for click detection
        EnsureRequiredComponents();
    }
    
    private void EnsureRequiredComponents()
    {
        // Ensure we have an Image component for the background
        Image backgroundImage = GetComponent<Image>();
        if (backgroundImage == null)
        {
            backgroundImage = gameObject.AddComponent<Image>();
            backgroundImage.color = new Color(0, 0, 0, 0.5f); // Semi-transparent black background
        }
        
        // Make sure the Image can receive raycasts
        backgroundImage.raycastTarget = true;
        
        // Apply the retro material if assigned
        if (retroMaterial != null)
        {
            backgroundImage.material = retroMaterial;
        }
    }
    
    private void Start()
    {
        // Subscribe to container events
        if (containerManager != null)
            containerManager.OnInventoryChanged += RefreshUI;
        
        // Initialize the grid
        GenerateGrid();
        
        // Configure item container
        ConfigureItemContainer();
        
        RefreshUI();
        
        // Initialize the dedicated highlight system
        InitializeHighlightSystem();
    }
    
    private void InitializeHighlightSystem()
    {
        if (highlightSystemInitialized) return;
        
        // Create highlight container
        if (highlightContainer != null)
            Destroy(highlightContainer);
        
        highlightContainer = new GameObject("ContainerGridHighlights");
        highlightContainer.transform.SetParent(itemContainer, false);
        
        // Set the highlight container to be first (bottom-most) in the hierarchy
        highlightContainer.transform.SetSiblingIndex(0);
        
        CanvasGroup containerCanvasGroup = highlightContainer.AddComponent<CanvasGroup>();
        containerCanvasGroup.alpha = 0.7f;
        
        int maxCells = 16; // Assuming no item is larger than 4x4
        cellHighlights = new GameObject[maxCells];
        
        for (int i = 0; i < maxCells; i++)
        {
            GameObject highlight = new GameObject($"Highlight_{i}");
            highlight.transform.SetParent(highlightContainer.transform, false);
            
            Image highlightImage = highlight.AddComponent<Image>();
            highlightImage.color = validPlacementColor;
            highlightImage.raycastTarget = false;
            
            RectTransform highlightRect = highlight.GetComponent<RectTransform>();
            highlightRect.anchorMin = new Vector2(0, 1);
            highlightRect.anchorMax = new Vector2(0, 1);
            highlightRect.pivot = new Vector2(0, 1);
            
            cellHighlights[i] = highlight;
            highlight.SetActive(false);
        }
        
        highlightContainer.SetActive(false);
        highlightSystemInitialized = true;
    }
    
    private void ConfigureItemContainer()
    {
        // Make sure the item container doesn't have a visible background
        Image itemContainerImage = itemContainer.GetComponent<Image>();
        if (itemContainerImage != null)
        {
            itemContainerImage.enabled = false;
        }
        
        // Check for any child objects in the item container that might be placeholder images
        for (int i = 0; i < itemContainer.childCount; i++)
        {
            Transform child = itemContainer.GetChild(i);
            // Skip if this is a functional item
            if (child.GetComponent<InventoryItemUI>() != null)
                continue;
            
            // Disable any placeholder images
            Image childImage = child.GetComponent<Image>();
            if (childImage != null)
            {
                childImage.enabled = false;
            }
        }
    }
    
    private void OnEnable()
    {
        if (containerManager != null)
            containerManager.OnInventoryChanged += RefreshUI;
        
        // Start opening animation
        if (animationCoroutine != null)
            StopCoroutine(animationCoroutine);
        animationCoroutine = StartCoroutine(AnimateInventory(true));
        
        // Ensure highlight system is initialized
        if (!highlightSystemInitialized)
        {
            InitializeHighlightSystem();
        }
        
        // Refresh UI when container is opened
        StartCoroutine(DelayedRefresh());
    }
    
    private void OnDisable()
    {
        if (containerManager != null)
            containerManager.OnInventoryChanged -= RefreshUI;
        
        // Stop any ongoing animation
        if (animationCoroutine != null)
            StopCoroutine(animationCoroutine);
        
        // Reset to closed state
        rectTransform.anchoredPosition = initialAnchoredPosition + closedOffset;
        canvasGroup.alpha = 0f;
    }
    
    private IEnumerator DelayedRefresh()
    {
        yield return new WaitForEndOfFrame();
        RefreshUI();
    }
    
    private IEnumerator AnimateInventory(bool opening)
    {
        Vector2 startPos = opening ? (initialAnchoredPosition + closedOffset) : initialAnchoredPosition;
        Vector2 endPos = opening ? initialAnchoredPosition : (initialAnchoredPosition + closedOffset);
        
        float startAlpha = opening ? 0f : 1f;
        float endAlpha = opening ? 1f : 0f;
        
        float elapsedTime = 0f;
        
        while (elapsedTime < animationDuration)
        {
            elapsedTime += Time.deltaTime;
            float normalizedTime = elapsedTime / animationDuration;
            float curveValue = animationCurve.Evaluate(normalizedTime);
            
            rectTransform.anchoredPosition = Vector2.Lerp(startPos, endPos, curveValue);
            canvasGroup.alpha = Mathf.Lerp(startAlpha, endAlpha, curveValue);
            
            yield return null;
        }
        
        rectTransform.anchoredPosition = endPos;
        canvasGroup.alpha = endAlpha;
        
        animationCoroutine = null;
    }
    
    // Calculate grid offset for positioning items
    private void CalculateGridOffset()
    {
        if (slotContainer == null) return;
        
        gridRectTransform = slotContainer.GetComponent<RectTransform>();
        if (gridRectTransform == null) return;
        
        // Get the world position of the grid's top-left corner
        Vector3[] corners = new Vector3[4];
        gridRectTransform.GetWorldCorners(corners);
        
        // Convert to local position relative to the item container
        Vector2 topLeftWorld = corners[1]; // Top-left corner
        Vector2 localPos = itemContainer.InverseTransformPoint(topLeftWorld);
        
        gridOffset = localPos;
    }
    
    public void OnPointerClick(PointerEventData eventData)
    {
        // Only handle left mouse button clicks
        if (eventData.button != PointerEventData.InputButton.Left) return;

        // Check if we clicked directly on the container background
        if (eventData.pointerPress == gameObject || eventData.pointerPress == null)
        {
            // Get and deselect the currently selected item
            var selectedItem = InventoryItemUIDrag.GetSelectedItem();
            if (selectedItem != null)
            {
                selectedItem.Deselect();
            }
        }
    }

    // Create the grid slots
    private void GenerateGrid()
    {
        // Clear existing slots
        foreach (Transform child in slotContainer)
            Destroy(child.gameObject);

        // Configure grid layout
        gridLayout = slotContainer.GetComponent<GridLayoutGroup>();
        if (gridLayout == null)
            gridLayout = slotContainer.gameObject.AddComponent<GridLayoutGroup>();

        gridLayout.constraint = GridLayoutGroup.Constraint.FixedColumnCount;
        gridLayout.constraintCount = containerManager.width;

        // Create slots
        for (int y = 0; y < containerManager.height; y++)
        {
            for (int x = 0; x < containerManager.width; x++)
            {
                GameObject slotObj = Instantiate(slotPrefab, slotContainer);
                ContainerInventorySlot slot = slotObj.GetComponent<ContainerInventorySlot>();
                if (slot != null)
                {
                    slot.Initialize(x, y);
                }
            }
        }

        // Calculate grid offset after creating slots
        StartCoroutine(DelayedGridOffsetCalculation());
    }

    private IEnumerator DelayedGridOffsetCalculation()
    {
        yield return new WaitForEndOfFrame();
        CalculateGridOffset();
    }

    // Update all item UI elements
    private void RefreshUI()
    {
        if (containerManager == null) return;

        // Track current items
        HashSet<int> currentItemKeys = new HashSet<int>();

        // First, reset ALL slots to empty state
        ResetAllSlots();

        // Add or update existing items
        foreach (var item in containerManager.GetAllItems())
        {
            // Skip items that don't exist anymore
            if (item == null)
            {
                Debug.LogError("Null item in container items list!");
                continue;
            }

            // Skip items with invalid positions
            if (item.position.x < 0 || item.position.y < 0 ||
                item.position.x >= containerManager.width ||
                item.position.y >= containerManager.height)
            {
                Debug.Log($"Item {item.GetName()} has invalid position {item.position} - skipping UI update");
                continue;
            }

            AddOrUpdateItemUI(item);
            currentItemKeys.Add(item.GetInstanceID());

            // Mark slots as occupied for this item
            MarkSlotsAsOccupied(item);
        }

        // Remove any items that are no longer in container
        RemoveStaleItemUIs(currentItemKeys);
    }

    // Reset all slots to empty state
    private void ResetAllSlots()
    {
        ContainerInventorySlot[] slots = slotContainer.GetComponentsInChildren<ContainerInventorySlot>();
        foreach (var slot in slots)
        {
            slot.SetOccupied(false);
        }
    }

    // Mark grid slots as occupied for a specific item
    private void MarkSlotsAsOccupied(InventoryItem item)
    {
        if (item == null) return;

        Vector2Int itemPos = item.position;
        Vector2Int itemSize = item.GetRotatedSize();

        // Update slot opacity for each cell the item occupies
        for (int x = 0; x < itemSize.x; x++)
        {
            for (int y = 0; y < itemSize.y; y++)
            {
                Vector2Int slotPos = new Vector2Int(itemPos.x + x, itemPos.y + y);

                // Validate position is in grid bounds
                if (slotPos.x < 0 || slotPos.y < 0 ||
                    slotPos.x >= containerManager.width ||
                    slotPos.y >= containerManager.height)
                {
                    continue;
                }

                ContainerInventorySlot slot = GetSlotAtPosition(slotPos);
                if (slot != null)
                {
                    slot.SetOccupied(true);
                }
            }
        }
    }

    // Get slot at specific grid position
    private ContainerInventorySlot GetSlotAtPosition(Vector2Int position)
    {
        ContainerInventorySlot[] slots = slotContainer.GetComponentsInChildren<ContainerInventorySlot>();
        foreach (var slot in slots)
        {
            if (slot.gridPosition == position)
                return slot;
        }
        return null;
    }

    // Remove stale item UIs that are no longer in the container
    private void RemoveStaleItemUIs(HashSet<int> currentItemKeys)
    {
        List<int> keysToRemove = new List<int>();

        foreach (var kvp in itemUIMapping)
        {
            if (!currentItemKeys.Contains(kvp.Key))
            {
                keysToRemove.Add(kvp.Key);
            }
        }

        foreach (int key in keysToRemove)
        {
            if (itemUIMapping.TryGetValue(key, out InventoryItemUI itemUI))
            {
                Destroy(itemUI.gameObject);
                itemUIMapping.Remove(key);
            }
        }
    }

    // Add or update an item UI
    private void AddOrUpdateItemUI(InventoryItem item)
    {
        int key = item.GetInstanceID();

        if (itemUIMapping.TryGetValue(key, out InventoryItemUI existingItemUI))
        {
            // Update existing item
            UpdateItemUIPosition(existingItemUI, item);
        }
        else
        {
            // Create new item UI
            CreateNewItemUI(item, key);
        }
    }

    // Create a new item UI
    private void CreateNewItemUI(InventoryItem item, int key)
    {
        GameObject itemUIObj = Instantiate(itemUIPrefab, itemContainer);
        InventoryItemUI itemUI = itemUIObj.GetComponent<InventoryItemUI>();

        if (itemUI != null)
        {
            // Configure the item UI
            UpdateItemUIPosition(itemUI, item);

            // Configure image component
            Image image = itemUIObj.GetComponent<Image>();
            if (image != null)
            {
                image.raycastTarget = true;
                image.preserveAspect = false;
                image.type = Image.Type.Simple;
                image.color = Color.white;
            }

            // Set up drag component
            ContainerInventoryItemUIDrag dragComponent = itemUIObj.GetComponent<ContainerInventoryItemUIDrag>();
            if (dragComponent != null)
                dragComponent.backendItem = item;
            else
                Debug.LogWarning("ContainerInventoryItemUIDrag component not found on itemUIPrefab!");

            itemUIMapping.Add(key, itemUI);
        }
    }

    // Update item UI position and properties
    private void UpdateItemUIPosition(InventoryItemUI itemUI, InventoryItem item)
    {
        RectTransform rect = itemUI.GetComponent<RectTransform>();

        // Set anchoring to top-left
        rect.pivot = new Vector2(0, 1);
        rect.anchorMin = new Vector2(0, 1);
        rect.anchorMax = new Vector2(0, 1);

        // Calculate grid position
        Vector2 itemPos = new Vector2(
            gridOffset.x + (item.position.x * (slotSize.x + gridLayout.spacing.x)),
            gridOffset.y - (item.position.y * (slotSize.y + gridLayout.spacing.y))
        );

        // Set target position for lerping
        itemUI.SetTargetPosition(itemPos);

        try
        {
            itemUI.Set(item.itemData, item.isRotated, slotSize);
            // Update text displays for stack count and ammo
            itemUI.UpdateItemTexts(item);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error setting item data: {e.Message}");
        }
    }

    // Remove a specific item UI
    public void RemoveItemUI(InventoryItem item)
    {
        int key = item.GetInstanceID();
        if (itemUIMapping.TryGetValue(key, out InventoryItemUI itemUI))
        {
            Destroy(itemUI.gameObject);
            itemUIMapping.Remove(key);
        }
    }

    // Show grid highlights for dragged item
    public void ShowGridHighlights(Vector2Int gridPos, Vector2Int size, bool isValid)
    {
        if (!highlightSystemInitialized)
        {
            InitializeHighlightSystem();
        }

        if (highlightContainer == null || cellHighlights == null) return;

        highlightContainer.SetActive(true);

        Color highlightColor = isValid ? validPlacementColor : invalidPlacementColor;
        int cellIndex = 0;

        // Show highlights for each cell the item would occupy
        for (int x = 0; x < size.x; x++)
        {
            for (int y = 0; y < size.y; y++)
            {
                int gridX = gridPos.x + x;
                int gridY = gridPos.y + y;

                if (gridX >= 0 && gridX < containerManager.width &&
                    gridY >= 0 && gridY < containerManager.height &&
                    cellIndex < cellHighlights.Length)
                {
                    GameObject highlight = cellHighlights[cellIndex];
                    highlight.SetActive(true);

                    Image highlightImage = highlight.GetComponent<Image>();
                    highlightImage.color = highlightColor;

                    RectTransform highlightRect = highlight.GetComponent<RectTransform>();
                    highlightRect.sizeDelta = slotSize;

                    Vector2 highlightPos = new Vector2(
                        gridOffset.x + (gridX * (slotSize.x + gridLayout.spacing.x)),
                        gridOffset.y - (gridY * (slotSize.y + gridLayout.spacing.y))
                    );

                    highlightRect.anchoredPosition = highlightPos;
                    cellIndex++;
                }
            }
        }

        // Hide unused highlights
        for (int i = cellIndex; i < cellHighlights.Length; i++)
        {
            cellHighlights[i].SetActive(false);
        }
    }

    // Hide grid highlights
    public void HideGridHighlights()
    {
        if (highlightContainer != null)
        {
            highlightContainer.SetActive(false);
        }
    }
}
