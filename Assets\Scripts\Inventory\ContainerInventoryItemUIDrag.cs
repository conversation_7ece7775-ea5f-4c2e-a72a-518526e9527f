using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

[RequireComponent(typeof(CanvasGroup))]
public class ContainerInventoryItemUIDrag : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IBegin<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON><PERSON>, IEndDrag<PERSON><PERSON>ler, IPointerClickHandler
{
    private RectTransform rectTransform;
    private CanvasGroup canvasGroup;
    private ContainerInventoryUI containerUI;
    private Image itemImage;
    private Vector2 dragOffset;
    private Vector2Int adjustedGridPosition;
    
    // Drag state
    private bool isDragging;
    public bool temporaryIsRotated;
    private Vector2Int originalGridPosition;
    
    // Grid highlight objects
    private GameObject highlightContainer;
    private GameObject[] cellHighlights;
    
    [Header("UI Colors")]
    [Tooltip("Color for valid placement indicators")]
    [SerializeField] private Color validPlacementColor = new Color(0.5f, 1f, 0.5f, 0.5f);
    
    [Tooltip("Color for invalid placement indicators")]
    [SerializeField] private Color invalidPlacementColor = new Color(1f, 0.5f, 0.5f, 0.5f);
    
    [Tooltip("Alpha value for item during drag")]
    [Range(0.1f, 1f)]
    [SerializeField] private float dragAlpha = 0.6f;
    
    [Tooltip("Color tint for valid placement during drag")]
    [SerializeField] private Color validDragTint = new Color(1f, 1f, 1f, 0.6f);
    
    [Tooltip("Color tint for invalid placement during drag")]
    [SerializeField] private Color invalidDragTint = new Color(1f, 0.5f, 0.5f, 0.6f);
    
    // Reference to the backend item
    public InventoryItem backendItem;
    
    private void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
        canvasGroup = GetComponent<CanvasGroup>();
        containerUI = ContainerInventoryUI.Instance;
        itemImage = GetComponent<Image>();
    }
    
    public void OnBeginDrag(PointerEventData eventData)
    {
        if (backendItem == null) return;
        
        isDragging = true;
        originalGridPosition = backendItem.position;
        temporaryIsRotated = backendItem.isRotated;
        
        // Calculate drag offset
        Vector2 localPointerPosition;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            rectTransform, eventData.position, eventData.pressEventCamera, out localPointerPosition);
        dragOffset = localPointerPosition;
        
        // Set visual state for dragging
        canvasGroup.alpha = dragAlpha;
        canvasGroup.blocksRaycasts = false;
        
        // Bring to front
        transform.SetAsLastSibling();
        
        Debug.Log($"Started dragging container item: {backendItem.GetName()}");
    }
    
    public void OnDrag(PointerEventData eventData)
    {
        if (!isDragging || backendItem == null) return;
        
        // Update position
        Vector2 globalMousePos;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            containerUI.transform as RectTransform, eventData.position, eventData.pressEventCamera, out globalMousePos);
        
        rectTransform.anchoredPosition = globalMousePos - dragOffset;
        
        // Handle rotation input
        if (Input.GetKeyDown(KeyCode.R) && backendItem.itemData.canRotate)
        {
            temporaryIsRotated = !temporaryIsRotated;
            Debug.Log($"Rotated container item: {backendItem.GetName()}, new rotation: {temporaryIsRotated}");
        }
        
        // Update placement validity
        UpdatePlacementValidity();
    }
    
    public void OnEndDrag(PointerEventData eventData)
    {
        if (!isDragging) return;
        
        isDragging = false;
        
        // Reset visual state
        canvasGroup.alpha = 1f;
        canvasGroup.blocksRaycasts = true;
        itemImage.color = Color.white;
        
        // Hide highlights
        HideAllGridHighlights();
        
        // Check if dropped on player inventory
        if (IsPointerOverPlayerInventory(eventData.position))
        {
            TransferToPlayerInventory();
        }
        // Check if dropped on hotbar
        else if (IsPointerOverHotbar(eventData.position))
        {
            TransferToHotbar();
        }
        else
        {
            // Dropped within container or invalid area - let the slot handle it
            // If no valid drop occurred, the item will stay in its original position
        }
        
        Debug.Log($"Ended dragging container item: {backendItem.GetName()}");
    }
    
    private void UpdatePlacementValidity()
    {
        // Check if over player inventory
        if (IsPointerOverPlayerInventory(Input.mousePosition))
        {
            // Show player inventory highlights
            Vector2Int playerGridPos = CalculatePlayerGridPosition();
            Vector2Int size = temporaryIsRotated ?
                new Vector2Int(backendItem.itemData.size.y, backendItem.itemData.size.x) :
                backendItem.itemData.size;
            
            bool isValid = InventoryUI.Instance.inventoryManager.CanPlaceItem(backendItem, playerGridPos, size);
            itemImage.color = isValid ? validDragTint : invalidDragTint;
            
            InventoryUI.Instance.ShowGridHighlights(playerGridPos, size, isValid);
            containerUI.HideGridHighlights();
        }
        // Check if over container inventory
        else if (IsPointerOverContainerInventory(Input.mousePosition))
        {
            Vector2Int gridPosition = CalculateContainerGridPosition();
            adjustedGridPosition = gridPosition;
            
            Vector2Int size = temporaryIsRotated ?
                new Vector2Int(backendItem.itemData.size.y, backendItem.itemData.size.x) :
                backendItem.itemData.size;
            
            bool isValid = CanPlaceAt(adjustedGridPosition, size);
            
            itemImage.color = isValid ? validDragTint : invalidDragTint;
            
            containerUI.ShowGridHighlights(adjustedGridPosition, size, isValid);
            InventoryUI.Instance.HideGridHighlights();
        }
        else
        {
            // Not over any valid inventory
            itemImage.color = invalidDragTint;
            HideAllGridHighlights();
        }
    }
    
    private Vector2Int CalculateContainerGridPosition()
    {
        Vector2 localPoint;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            containerUI.itemContainer as RectTransform, Input.mousePosition, null, out localPoint);
        
        Vector2 gridOffset = containerUI.GridOffset;
        Vector2 spacing = containerUI.GridSpacing;
        
        int gridX = Mathf.FloorToInt((localPoint.x - gridOffset.x) / (containerUI.slotSize.x + spacing.x));
        int gridY = Mathf.FloorToInt((gridOffset.y - localPoint.y) / (containerUI.slotSize.y + spacing.y));
        
        return new Vector2Int(gridX, gridY);
    }
    
    private Vector2Int CalculatePlayerGridPosition()
    {
        Vector2 localPoint;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            InventoryUI.Instance.itemContainer as RectTransform, Input.mousePosition, null, out localPoint);
        
        Vector2 gridOffset = InventoryUI.Instance.GridOffset;
        Vector2 spacing = InventoryUI.Instance.GridSpacing;
        
        int gridX = Mathf.FloorToInt((localPoint.x - gridOffset.x) / (InventoryUI.Instance.slotSize.x + spacing.x));
        int gridY = Mathf.FloorToInt((gridOffset.y - localPoint.y) / (InventoryUI.Instance.slotSize.y + spacing.y));
        
        return new Vector2Int(gridX, gridY);
    }
    
    private bool CanPlaceAt(Vector2Int gridPos, Vector2Int size)
    {
        return containerUI.containerManager.CanPlaceItem(backendItem, gridPos, size);
    }
    
    private bool IsPointerOverPlayerInventory(Vector2 screenPosition)
    {
        if (InventoryUI.Instance == null || !InventoryUI.Instance.gameObject.activeInHierarchy)
            return false;
        
        return RectTransformUtility.RectangleContainsScreenPoint(
            InventoryUI.Instance.GridRectTransform, screenPosition, null);
    }
    
    private bool IsPointerOverContainerInventory(Vector2 screenPosition)
    {
        if (containerUI == null || !containerUI.gameObject.activeInHierarchy)
            return false;
        
        return RectTransformUtility.RectangleContainsScreenPoint(
            containerUI.GridRectTransform, screenPosition, null);
    }
    
    private bool IsPointerOverHotbar(Vector2 screenPosition)
    {
        Hotbar hotbar = FindObjectOfType<Hotbar>();
        if (hotbar == null) return false;
        
        return RectTransformUtility.RectangleContainsScreenPoint(
            hotbar.GetComponent<RectTransform>(), screenPosition, null);
    }
    
    private void TransferToPlayerInventory()
    {
        // Remove from container
        containerUI.containerManager.RemoveItem(backendItem);
        
        // Add to player inventory
        bool success = InventoryUI.Instance.inventoryManager.PickupItem(backendItem);
        
        if (!success)
        {
            // If failed, put back in container
            containerUI.containerManager.PickupItem(backendItem);
            Debug.Log("Failed to transfer item to player inventory - returned to container");
        }
        else
        {
            Debug.Log($"Transferred {backendItem.GetName()} from container to player inventory");
        }
    }
    
    private void TransferToHotbar()
    {
        Hotbar hotbar = FindObjectOfType<Hotbar>();
        if (hotbar == null) return;
        
        // Try to add to hotbar
        bool success = hotbar.TryAddItemToFirstEmptySlot(backendItem);
        
        if (success)
        {
            // Remove from container
            containerUI.containerManager.RemoveItem(backendItem);
            Debug.Log($"Transferred {backendItem.GetName()} from container to hotbar");
        }
        else
        {
            Debug.Log("Failed to transfer item to hotbar - no empty slots");
        }
    }
    
    private void HideAllGridHighlights()
    {
        containerUI.HideGridHighlights();
        if (InventoryUI.Instance != null)
            InventoryUI.Instance.HideGridHighlights();
    }
    
    public void OnPointerClick(PointerEventData eventData)
    {
        // Handle item selection/deselection if needed
        if (eventData.button == PointerEventData.InputButton.Left)
        {
            Debug.Log($"Clicked on container item: {backendItem.GetName()}");
        }
    }
}
