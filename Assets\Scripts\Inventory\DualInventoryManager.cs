using UnityEngine;

public class DualInventoryManager : MonoBehaviour
{
    [Header("References")]
    [<PERSON>lt<PERSON>("Reference to the player inventory UI")]
    public InventoryUI playerInventoryUI;
    
    [Tooltip("Reference to the container inventory UI")]
    public ContainerInventoryUI containerInventoryUI;
    
    // Singleton instance
    public static DualInventoryManager Instance { get; private set; }
    
    // State tracking
    private bool isDualInventoryOpen = false;
    private ContainerInteractable currentContainer = null;
    
    private void Awake()
    {
        // Set up singleton
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
            return;
        }
        
        // Find UI references if not assigned
        if (playerInventoryUI == null)
            playerInventoryUI = FindObjectOfType<InventoryUI>();
        
        if (containerInventoryUI == null)
            containerInventoryUI = FindObjectOfType<ContainerInventoryUI>();
    }
    
    private void Update()
    {
        // Handle global inventory closing with Escape key
        if (isDualInventoryOpen && Input.GetKeyDown(KeyCode.Escape))
        {
            CloseDualInventory();
        }

        // Don't handle Tab key - let the existing inventory system handle it
        // The DualInventoryManager should only manage dual inventory operations
    }
    
    /// <summary>
    /// Opens both player and container inventories
    /// </summary>
    /// <param name="container">The container being opened</param>
    public void OpenDualInventory(ContainerInteractable container)
    {
        if (isDualInventoryOpen) return;

        currentContainer = container;

        // Set up container UI with the specific container's manager
        if (containerInventoryUI != null)
        {
            containerInventoryUI.containerManager = container.GetContainerManager();
            containerInventoryUI.ShowContainerUI();
        }

        // Open player inventory
        if (playerInventoryUI != null)
        {
            playerInventoryUI.gameObject.SetActive(true);
        }

        isDualInventoryOpen = true;
        
        // Disable player movement (if you have a player controller)
        SetPlayerMovementEnabled(false);
        
        Debug.Log($"Opened dual inventory with container: {container.name}");
    }
    
    /// <summary>
    /// Closes both inventories
    /// </summary>
    public void CloseDualInventory()
    {
        if (!isDualInventoryOpen) return;

        // Close container UI
        if (containerInventoryUI != null)
        {
            containerInventoryUI.HideContainerUI();
            containerInventoryUI.containerManager = null;
        }

        // Close player inventory
        if (playerInventoryUI != null)
        {
            playerInventoryUI.gameObject.SetActive(false);
        }

        isDualInventoryOpen = false;
        currentContainer = null;

        // Re-enable player movement
        SetPlayerMovementEnabled(true);

        Debug.Log("Closed dual inventory");
    }
    
    /// <summary>
    /// This method is no longer used - the existing inventory system handles player inventory toggling
    /// DualInventoryManager only manages dual inventory operations (player + container)
    /// </summary>
    [System.Obsolete("Use the existing inventory system for player inventory toggling")]
    public void TogglePlayerInventory()
    {
        // This method is deprecated - let the existing inventory system handle player inventory
        Debug.LogWarning("TogglePlayerInventory is deprecated. Use the existing inventory system instead.");
    }
    
    /// <summary>
    /// Checks if dual inventory is currently open
    /// </summary>
    public bool IsDualInventoryOpen()
    {
        return isDualInventoryOpen;
    }
    
    /// <summary>
    /// Gets the currently open container (if any)
    /// </summary>
    public ContainerInteractable GetCurrentContainer()
    {
        return currentContainer;
    }
    
    /// <summary>
    /// Enables or disables player movement - ONLY for dual inventory operations
    /// Regular inventory cursor management should be handled by the existing inventory system
    /// </summary>
    private void SetPlayerMovementEnabled(bool enabled)
    {
        // Only manage player movement, not cursor state
        // Let the existing inventory system handle cursor management
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null)
        {
            playerController.enabled = enabled;
        }

        // Don't manage cursor state here - let the existing inventory system handle it
        // This prevents conflicts between dual inventory and regular inventory cursor management
    }
    
    /// <summary>
    /// Force cleanup of any stuck drag visuals in both inventories
    /// </summary>
    public void ForceCleanupAllDragVisuals()
    {
        if (playerInventoryUI != null)
        {
            playerInventoryUI.ForceCleanupDragVisuals();
        }
        
        if (containerInventoryUI != null)
        {
            containerInventoryUI.HideGridHighlights();
        }
        
        // Clear any hotbar drag visuals
        HotbarSlot[] hotbarSlots = FindObjectsOfType<HotbarSlot>();
        foreach (var slot in hotbarSlots)
        {
            slot.ForceCleanupDragVisuals();
        }
        
        Debug.Log("Forced cleanup of all inventory drag visuals");
    }
    
    /// <summary>
    /// Check if any inventory is currently open
    /// </summary>
    public bool IsAnyInventoryOpen()
    {
        bool playerOpen = playerInventoryUI != null && playerInventoryUI.gameObject.activeInHierarchy;
        bool containerOpen = containerInventoryUI != null && containerInventoryUI.gameObject.activeInHierarchy;
        
        return playerOpen || containerOpen;
    }
    
    /// <summary>
    /// Transfer an item from player inventory to current container
    /// </summary>
    public bool TransferToContainer(InventoryItem item)
    {
        if (!isDualInventoryOpen || currentContainer == null) return false;
        
        ContainerInventoryManager containerManager = currentContainer.GetContainerManager();
        if (containerManager == null) return false;
        
        // Remove from player inventory
        playerInventoryUI.inventoryManager.RemoveItem(item);
        
        // Try to add to container
        bool success = containerManager.PickupItem(item);
        
        if (!success)
        {
            // If failed, put back in player inventory
            playerInventoryUI.inventoryManager.PickupItem(item);
            return false;
        }
        
        return true;
    }
    
    /// <summary>
    /// Transfer an item from current container to player inventory
    /// </summary>
    public bool TransferToPlayer(InventoryItem item)
    {
        if (!isDualInventoryOpen || currentContainer == null) return false;
        
        ContainerInventoryManager containerManager = currentContainer.GetContainerManager();
        if (containerManager == null) return false;
        
        // Remove from container
        containerManager.RemoveItem(item);
        
        // Try to add to player inventory
        bool success = playerInventoryUI.inventoryManager.PickupItem(item);
        
        if (!success)
        {
            // If failed, put back in container
            containerManager.PickupItem(item);
            return false;
        }
        
        return true;
    }
}
