using System.Collections.Generic;
using UnityEngine;

public class ContainerInventoryManager : MonoBehaviour
{
    [Header("Container Configuration")]
    public ContainerData containerData;
    
    [Header("Runtime Data")]
    public int width = 4;
    public int height = 4;
    
    // Event for inventory changes
    public delegate void UpdateInventoryChange();
    public event UpdateInventoryChange OnInventoryChanged;
    
    // Internal data storage
    private InventoryItem[,] inventoryGrid;
    public List<InventoryItem> items = new List<InventoryItem>();
    
    // Track if container has been initialized
    private bool isInitialized = false;
    
    private void Awake()
    {
        // Initialize from container data if available
        if (containerData != null)
        {
            width = containerData.width;
            height = containerData.height;
        }
        
        // Initialize the grid
        inventoryGrid = new InventoryItem[width, height];
    }
    
    private void Start()
    {
        // Initialize starting items if this is the first time opening
        if (!isInitialized && containerData != null)
        {
            InitializeStartingItems();
            isInitialized = true;
        }
    }
    
    private void InitializeStartingItems()
    {
        foreach (var startingItem in containerData.startingItems)
        {
            if (startingItem.itemData != null)
            {
                // Create the item GameObject
                GameObject itemObj = new GameObject(startingItem.itemData.itemName);
                InventoryItem item = itemObj.AddComponent<InventoryItem>();
                
                // Set up the item
                item.itemData = startingItem.itemData;
                item.stackCount = startingItem.stackCount;
                item.currentAmmo = startingItem.currentAmmo;
                item.isRotated = startingItem.isRotated;
                
                // Try to add it to the container
                if (TryAddItem(item, startingItem.position, startingItem.isRotated))
                {
                    Debug.Log($"Added starting item {item.GetName()} to container at {startingItem.position}");
                }
                else
                {
                    Debug.LogWarning($"Could not place starting item {item.GetName()} at {startingItem.position}");
                    Destroy(itemObj);
                }
            }
        }
    }
    
    #region Item Management
    
    // Try to add an item at a specific position
    public bool TryAddItem(InventoryItem item, Vector2Int position, bool rotated)
    {
        // Get size based on rotation
        Vector2Int size = item.GetSize(rotated);
        
        // Verify position is valid
        if (!CanPlaceItem(item, position, size))
            return false;
        
        // Place the item in the grid
        PlaceItemInGrid(item, position, size);
        
        // Update item properties
        item.position = position;
        
        // Set the rotation state
        if (rotated != item.isRotated)
            item.SetRotation(rotated);
        
        // Ensure item is fully initialized
        InitializeInventoryItem(item);
        
        // Add to inventory
        items.Add(item);
        item.transform.SetParent(transform);
        item.OnPickup();
        
        Debug.Log($"Added item {item.GetName()} to container at position {position}, rotation: {rotated}");
        
        return true;
    }
    
    // Add an item to the first available position
    public bool PickupItem(InventoryItem item)
    {
        if (item == null)
        {
            Debug.LogError("Cannot pickup null item");
            return false;
        }
        
        if (item.itemData == null)
        {
            Debug.LogError($"Item {item.name} has no itemData attached");
            return false;
        }
        
        // Check for available position with rotation optimization
        FindPositionResult result = FindFirstAvailablePositionWithRotation(item);
        
        // Check if position is valid
        if (!result.found)
        {
            Debug.Log($"No space available for item {item.itemData.itemName} in container, even with rotation");
            return false;
        }
        
        // Try to add the item with the appropriate rotation
        try
        {
            if (TryAddItem(item, result.position, result.shouldRotate))
            {
                // Trigger UI update
                OnInventoryChanged?.Invoke();
                return true;
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error adding item to container: {e.Message}");
        }
        
        return false;
    }
    
    // Remove an item from the container
    public void RemoveItem(InventoryItem item)
    {
        // Clear grid cells
        RemoveItemFromGrid(item);
        
        // Remove from item list
        items.Remove(item);
        
        // Notify listeners
        OnInventoryChanged?.Invoke();
    }
    
    // Ensure item is fully initialized when added to container
    private void InitializeInventoryItem(InventoryItem item)
    {
        if (item == null || item.itemData == null) return;
        
        switch (item.itemData.itemType)
        {
            case ItemType.Weapon:
                // If not already set, start with empty magazine
                if (item.currentAmmo < 0 || item.currentAmmo > item.itemData.magazineSize)
                    item.currentAmmo = 0;
                break;
            case ItemType.Ammo:
                // Set stack count to default if not set
                if (item.stackCount <= 0)
                    item.stackCount = item.itemData.maxStackSize > 0 ? item.itemData.maxStackSize : 30;
                break;
        }
        Debug.Log($"[ContainerInventoryManager] Initialized item: {item.GetName()} | Ammo: {item.currentAmmo} | Stack: {item.stackCount}");
    }
    
    #endregion
    
    #region Grid Management
    
    // Check if an item can be placed at a specific position
    public bool CanPlaceItem(InventoryItem item, Vector2Int position, Vector2Int size)
    {
        // Check bounds
        if (position.x < 0 || position.y < 0 ||
            position.x + size.x > width ||
            position.y + size.y > height)
        {
            return false;
        }
        
        // Check for overlapping items
        for (int x = 0; x < size.x; x++)
        {
            for (int y = 0; y < size.y; y++)
            {
                Vector2Int checkPos = new Vector2Int(position.x + x, position.y + y);
                InventoryItem existingItem = inventoryGrid[checkPos.x, checkPos.y];
                
                // If there's an item and it's not the same item we're trying to place
                if (existingItem != null && existingItem != item)
                {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    // Place an item in the grid
    private void PlaceItemInGrid(InventoryItem item, Vector2Int position, Vector2Int size)
    {
        for (int x = 0; x < size.x; x++)
        {
            for (int y = 0; y < size.y; y++)
            {
                inventoryGrid[position.x + x, position.y + y] = item;
            }
        }
    }
    
    // Remove an item from the grid
    private void RemoveItemFromGrid(InventoryItem item)
    {
        for (int x = 0; x < width; x++)
        {
            for (int y = 0; y < height; y++)
            {
                if (inventoryGrid[x, y] == item)
                {
                    inventoryGrid[x, y] = null;
                }
            }
        }
    }
    
    #endregion

    #region Position Finding

    // Structure to hold position search results
    public struct FindPositionResult
    {
        public bool found;
        public Vector2Int position;
        public bool shouldRotate;
    }

    // Find the first available position for an item, trying rotation if needed
    private FindPositionResult FindFirstAvailablePositionWithRotation(InventoryItem item)
    {
        Vector2Int originalSize = item.itemData.size;
        Vector2Int rotatedSize = new Vector2Int(originalSize.y, originalSize.x);

        // Try original orientation first
        for (int y = 0; y <= height - originalSize.y; y++)
        {
            for (int x = 0; x <= width - originalSize.x; x++)
            {
                Vector2Int position = new Vector2Int(x, y);
                if (CanPlaceItem(item, position, originalSize))
                {
                    return new FindPositionResult { found = true, position = position, shouldRotate = false };
                }
            }
        }

        // If item can rotate and original orientation didn't work, try rotated
        if (item.itemData.canRotate && originalSize.x != originalSize.y)
        {
            for (int y = 0; y <= height - rotatedSize.y; y++)
            {
                for (int x = 0; x <= width - rotatedSize.x; x++)
                {
                    Vector2Int position = new Vector2Int(x, y);
                    if (CanPlaceItem(item, position, rotatedSize))
                    {
                        return new FindPositionResult { found = true, position = position, shouldRotate = true };
                    }
                }
            }
        }

        return new FindPositionResult { found = false, position = Vector2Int.zero, shouldRotate = false };
    }

    // Update an item's position in the grid
    public bool UpdateItemPosition(InventoryItem item, Vector2Int newPosition, Vector2Int newSize, bool isRotated)
    {
        if (item == null) return false;

        // Remove item from current position
        RemoveItemFromGrid(item);

        // Check if new position is valid
        if (!CanPlaceItem(item, newPosition, newSize))
        {
            // If invalid, put it back in original position
            Vector2Int originalSize = item.GetRotatedSize();
            PlaceItemInGrid(item, item.position, originalSize);
            return false;
        }

        // Place item in new position
        PlaceItemInGrid(item, newPosition, newSize);
        item.position = newPosition;

        // Update rotation if needed
        if (isRotated != item.isRotated)
            item.SetRotation(isRotated);

        // Notify listeners
        OnInventoryChanged?.Invoke();

        return true;
    }

    #endregion

    #region Utility Methods

    // Get all items in the container
    public List<InventoryItem> GetAllItems()
    {
        return new List<InventoryItem>(items);
    }

    // Get the inventory grid
    public InventoryItem[,] GetInventoryGrid()
    {
        return inventoryGrid;
    }

    #endregion
}
