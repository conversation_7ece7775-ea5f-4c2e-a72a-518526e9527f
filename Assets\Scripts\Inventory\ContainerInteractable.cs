using UnityEngine;

public class ContainerInteractable : MonoBehaviour
{
    [<PERSON><PERSON>("Container Settings")]
    [Tooltip("The container data that defines this container's properties")]
    public ContainerData containerData;
    
    [Header("Interaction Settings")]
    [Tooltip("Distance within which player can interact with this container")]
    public float interactionDistance = 2f;
    
    [Toolt<PERSON>("Key to press for interaction")]
    public KeyCode interactionKey = KeyCode.E;
    
    [Head<PERSON>("Visual Feedback")]
    [Tooltip("UI element to show when player is near (optional)")]
    public GameObject interactionPrompt;
    
    // References
    private Transform playerTransform;
    private bool playerInRange = false;
    private bool isContainerOpen = false;
    
    // Container manager instance for this specific container
    private ContainerInventoryManager containerManager;
    
    private void Start()
    {
        // Find the player
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            playerTransform = player.transform;
        }
        else
        {
            Debug.LogWarning("Player not found! Make sure player GameObject has 'Player' tag.");
        }
        
        // Create container manager for this container
        SetupContainerManager();
        
        // Hide interaction prompt initially
        if (interactionPrompt != null)
            interactionPrompt.SetActive(false);
    }
    
    private void SetupContainerManager()
    {
        // Create a child GameObject to hold the container manager
        GameObject managerObj = new GameObject("ContainerManager");
        managerObj.transform.SetParent(transform);
        
        // Add the container manager component
        containerManager = managerObj.AddComponent<ContainerInventoryManager>();
        
        // Assign the container data
        if (containerData != null)
        {
            containerManager.containerData = containerData;
        }
        else
        {
            Debug.LogWarning($"No container data assigned to {gameObject.name}!");
        }
    }
    
    private void Update()
    {
        if (playerTransform == null) return;
        
        // Check distance to player
        float distance = Vector3.Distance(transform.position, playerTransform.position);
        bool wasInRange = playerInRange;
        playerInRange = distance <= interactionDistance;
        
        // Handle entering/leaving interaction range
        if (playerInRange && !wasInRange)
        {
            OnPlayerEnterRange();
        }
        else if (!playerInRange && wasInRange)
        {
            OnPlayerExitRange();
        }
        
        // Handle interaction input
        if (playerInRange && Input.GetKeyDown(interactionKey))
        {
            ToggleContainer();
        }
        
        // Handle closing container with Escape
        if (isContainerOpen && Input.GetKeyDown(KeyCode.Escape))
        {
            CloseContainer();
        }
    }
    
    private void OnPlayerEnterRange()
    {
        // Show interaction prompt
        if (interactionPrompt != null)
            interactionPrompt.SetActive(true);
        
        Debug.Log($"Can interact with {gameObject.name} - Press {interactionKey}");
    }
    
    private void OnPlayerExitRange()
    {
        // Hide interaction prompt
        if (interactionPrompt != null)
            interactionPrompt.SetActive(false);
        
        // Close container if it's open
        if (isContainerOpen)
        {
            CloseContainer();
        }
    }
    
    private void ToggleContainer()
    {
        if (isContainerOpen)
        {
            CloseContainer();
        }
        else
        {
            OpenContainer();
        }
    }
    
    private void OpenContainer()
    {
        if (isContainerOpen) return;

        // Use the DualInventoryManager to open both inventories
        DualInventoryManager dualManager = DualInventoryManager.Instance;
        if (dualManager != null)
        {
            dualManager.OpenDualInventory(this);
            isContainerOpen = true;
            Debug.Log($"Opened container: {containerData?.containerName ?? gameObject.name}");
        }
        else
        {
            Debug.LogError("DualInventoryManager instance not found!");
        }
    }

    private void CloseContainer()
    {
        if (!isContainerOpen) return;

        // Use the DualInventoryManager to close both inventories
        DualInventoryManager dualManager = DualInventoryManager.Instance;
        if (dualManager != null)
        {
            dualManager.CloseDualInventory();
        }

        isContainerOpen = false;

        Debug.Log($"Closed container: {containerData?.containerName ?? gameObject.name}");
    }
    
    // Public method to check if container is currently open
    public bool IsOpen()
    {
        return isContainerOpen;
    }
    
    // Public method to get the container manager
    public ContainerInventoryManager GetContainerManager()
    {
        return containerManager;
    }
    
    // Gizmo for visualizing interaction range in editor
    private void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireCircle(transform.position, interactionDistance);
    }
}
