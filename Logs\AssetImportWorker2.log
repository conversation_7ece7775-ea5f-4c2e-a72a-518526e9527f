Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.33f1 (b2c853adf198) revision 11716691'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'en' Physical Memory: 16087 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.33f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Users/<USER>/Hydra Games/Void Voyager
-logFile
Logs/AssetImportWorker2.log
-srvPort
65324
Successfully changed project path to: C:/Users/<USER>/Hydra Games/Void Voyager
C:/Users/<USER>/Hydra Games/Void Voyager
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [112472] Host "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2552495742 [EditorId] 2552495742 [Version] 1048832 [Id] WindowsEditor(7,MSI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [112472] Host "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2552495742 [EditorId] 2552495742 [Version] 1048832 [Id] WindowsEditor(7,MSI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
Refreshing native plugins compatible for Editor in 5687.76 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.33f1 (b2c853adf198)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Hydra Games/Void Voyager/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2820)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.7652
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56340
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.982509 seconds.
- Loaded All Assemblies, in 20.013 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.195 seconds
Domain Reload Profiling: 20207ms
	BeginReloadAssembly (17209ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (1028ms)
	LoadAllAssembliesAndSetupDomain (1739ms)
		LoadAssemblies (17206ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1736ms)
			TypeCache.Refresh (1734ms)
				TypeCache.ScanAssembly (1720ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (196ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (154ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (108ms)
			ProcessInitializeOnLoadMethodAttributes (30ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 23.702 seconds
Refreshing native plugins compatible for Editor in 4.38 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.570 seconds
Domain Reload Profiling: 24270ms
	BeginReloadAssembly (108ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (16ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (23536ms)
		LoadAssemblies (23237ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (361ms)
			TypeCache.Refresh (336ms)
				TypeCache.ScanAssembly (110ms)
			ScanForSourceGeneratedMonoScriptInfo (17ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (570ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (450ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (325ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.08 seconds
Refreshing native plugins compatible for Editor in 5.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5809 Unused Serialized files (Serialized files now loaded: 0)
Unloading 87 unused Assets / (351.0 KB). Loaded Objects now: 6267.
Memory consumption went from 225.4 MB to 225.1 MB.
Total: 4.721700 ms (FindLiveObjects: 0.570500 ms CreateObjectMapping: 0.191500 ms MarkObjects: 3.646000 ms  DeleteObjects: 0.312600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/ContainerInventoryManager.cs: b273ea968b95a31f181986c9722dc7ed -> 3d4a7c072dd0c73d7b6da207a2f8569a
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:scripting/monoscript/fileName/ContainerInventoryUI.cs: df2123fd1cb92c88fb7aab237502f466 -> 88dfaac17f58fe2b32980563e048d142
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/ContainerInventorySlot.cs: 907ff19ad9db78ffa506cdbe9261b56e -> 70e1281374b391e2249e545097886bd4
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/ContainerInventoryItemUIDrag.cs: 19ecc8fd55b838199ab4cfa845b911fb -> 1b6028197cfee9368250ce7e1ab09a46
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/ContainerData.cs: fb36d64792365dfcc89f24af8a4a210a -> 8239b81744d1bd531e171e5ab949f2a2
  custom:scripting/monoscript/fileName/ContainerInteractable.cs: 7847e4e9f48dd6d5bbc7a8a0d75805d1 -> 6676d72754ac51f99ae4e13bec722b5d
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.439 seconds
Refreshing native plugins compatible for Editor in 4.58 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 34.805 seconds
Domain Reload Profiling: 35241ms
	BeginReloadAssembly (143ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (234ms)
		LoadAssemblies (285ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (16ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (34805ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (468ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (66ms)
			ProcessInitializeOnLoadAttributes (322ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 10.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5641 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (321.7 KB). Loaded Objects now: 6273.
Memory consumption went from 195.3 MB to 195.0 MB.
Total: 4.812900 ms (FindLiveObjects: 0.527300 ms CreateObjectMapping: 0.315600 ms MarkObjects: 3.753500 ms  DeleteObjects: 0.214800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/ContainerInventoryManager.cs: b273ea968b95a31f181986c9722dc7ed -> 3d4a7c072dd0c73d7b6da207a2f8569a
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:scripting/monoscript/fileName/ContainerInventoryUI.cs: df2123fd1cb92c88fb7aab237502f466 -> 88dfaac17f58fe2b32980563e048d142
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/ContainerInventorySlot.cs: 907ff19ad9db78ffa506cdbe9261b56e -> 70e1281374b391e2249e545097886bd4
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/ContainerInventoryItemUIDrag.cs: 19ecc8fd55b838199ab4cfa845b911fb -> 1b6028197cfee9368250ce7e1ab09a46
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/ContainerData.cs: fb36d64792365dfcc89f24af8a4a210a -> 8239b81744d1bd531e171e5ab949f2a2
  custom:scripting/monoscript/fileName/ContainerInteractable.cs: 7847e4e9f48dd6d5bbc7a8a0d75805d1 -> 6676d72754ac51f99ae4e13bec722b5d
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.467 seconds
Refreshing native plugins compatible for Editor in 5.34 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.290 seconds
Domain Reload Profiling: 1752ms
	BeginReloadAssembly (147ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (245ms)
		LoadAssemblies (304ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (18ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1290ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (480ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (321ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 10.19 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5641 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (321.7 KB). Loaded Objects now: 6278.
Memory consumption went from 195.3 MB to 195.0 MB.
Total: 4.394800 ms (FindLiveObjects: 0.411000 ms CreateObjectMapping: 0.287000 ms MarkObjects: 3.538700 ms  DeleteObjects: 0.156300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/ContainerInventoryManager.cs: b273ea968b95a31f181986c9722dc7ed -> 3d4a7c072dd0c73d7b6da207a2f8569a
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:scripting/monoscript/fileName/ContainerInventoryUI.cs: df2123fd1cb92c88fb7aab237502f466 -> 88dfaac17f58fe2b32980563e048d142
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/ContainerInventorySlot.cs: 907ff19ad9db78ffa506cdbe9261b56e -> 70e1281374b391e2249e545097886bd4
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/ContainerInventoryItemUIDrag.cs: 19ecc8fd55b838199ab4cfa845b911fb -> 1b6028197cfee9368250ce7e1ab09a46
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/ContainerData.cs: fb36d64792365dfcc89f24af8a4a210a -> 8239b81744d1bd531e171e5ab949f2a2
  custom:scripting/monoscript/fileName/ContainerInteractable.cs: 7847e4e9f48dd6d5bbc7a8a0d75805d1 -> 6676d72754ac51f99ae4e13bec722b5d
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
TcpMessagingSession - receive error: operation aborted. errorcode: 995, details: The I/O operation has been aborted because of either a thread exit or an application request.
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0