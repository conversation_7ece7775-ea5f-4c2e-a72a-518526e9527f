using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class ContainerInventorySlot : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rop<PERSON><PERSON><PERSON>, IPointerEnterHandler, IPointerExitHandler
{
    private Image slotImage;
    private Color defaultColor;
    public Vector2Int gridPosition;
    private ContainerInventoryUI containerUI;
    
    [Header("Slot Settings")]
    [SerializeField] private float occupiedSlotAlpha = 0.3f; // Alpha value when slot is occupied
    [SerializeField] private float emptySlotAlpha = 0.8f;    // Alpha value when slot is empty
    
    private void Awake()
    {
        slotImage = GetComponent<Image>();
        defaultColor = slotImage.color;
        defaultColor.a = emptySlotAlpha;
        slotImage.color = defaultColor;
        containerUI = ContainerInventoryUI.Instance;
    }
    
    // Set the grid position for this slot
    public void SetGridPosition(int x, int y)
    {
        gridPosition = new Vector2Int(x, y);
    }
    
    // Initialize the slot with position
    public void Initialize(int x, int y)
    {
        SetGridPosition(x, y);
        gameObject.name = $"ContainerSlot_{x}_{y}";
    }
    
    // Set whether this slot is occupied
    public void SetOccupied(bool occupied)
    {
        Color newColor = defaultColor;
        newColor.a = occupied ? occupiedSlotAlpha : emptySlotAlpha;
        slotImage.color = newColor;
    }
    
    public void OnPointerEnter(PointerEventData eventData)
    {
        // Visual feedback when hovering over slot
        // Could add hover effects here if needed
    }
    
    public void OnPointerExit(PointerEventData eventData)
    {
        // Reset visual feedback when leaving slot
        // Could reset hover effects here if needed
    }
    
    public void OnDrop(PointerEventData eventData)
    {
        // Get the dragged item
        GameObject draggedObject = eventData.pointerDrag;
        if (draggedObject == null) return;
        
        // Check if it's from player inventory
        InventoryItemUIDrag playerDragComponent = draggedObject.GetComponent<InventoryItemUIDrag>();
        if (playerDragComponent != null)
        {
            // Handle transfer from player inventory to container
            HandlePlayerToContainerTransfer(playerDragComponent);
            return;
        }
        
        // Check if it's from container inventory
        ContainerInventoryItemUIDrag containerDragComponent = draggedObject.GetComponent<ContainerInventoryItemUIDrag>();
        if (containerDragComponent != null)
        {
            // Handle movement within container
            HandleContainerInternalMove(containerDragComponent);
            return;
        }
        
        // Check if it's from hotbar
        HotbarSlot hotbarSlot = draggedObject.GetComponent<HotbarSlot>();
        if (hotbarSlot != null && hotbarSlot.storedItem != null)
        {
            // Handle transfer from hotbar to container
            HandleHotbarToContainerTransfer(hotbarSlot);
            return;
        }
    }
    
    private void HandlePlayerToContainerTransfer(InventoryItemUIDrag dragComponent)
    {
        InventoryItem draggedItem = dragComponent.backendItem;
        if (draggedItem == null) return;
        
        bool isRotated = dragComponent.temporaryIsRotated;
        Vector2Int itemSize = isRotated ? 
            new Vector2Int(draggedItem.itemData.size.y, draggedItem.itemData.size.x) : 
            draggedItem.itemData.size;
        
        bool canPlace = false;
        bool updateSuccess = false;
        
        if (containerUI != null && containerUI.containerManager != null)
        {
            // Check if container can accept the item
            canPlace = containerUI.containerManager.CanPlaceItem(draggedItem, gridPosition, itemSize);
            if (canPlace)
            {
                // Remove from player inventory
                InventoryUI.Instance.inventoryManager.RemoveItem(draggedItem);
                
                // Add to container inventory
                updateSuccess = containerUI.containerManager.TryAddItem(draggedItem, gridPosition, isRotated);
                
                if (!updateSuccess)
                {
                    // If container add failed, put back in player inventory
                    InventoryUI.Instance.inventoryManager.PickupItem(draggedItem);
                }
            }
        }
        
        if (canPlace && updateSuccess)
        {
            Debug.Log($"Item transferred from player to container at grid position: {gridPosition}");
        }
        else
        {
            Debug.Log($"Cannot transfer item to container at grid position: {gridPosition}");
        }
    }
    
    private void HandleContainerInternalMove(ContainerInventoryItemUIDrag dragComponent)
    {
        InventoryItem draggedItem = dragComponent.backendItem;
        if (draggedItem == null) return;
        
        bool isRotated = dragComponent.temporaryIsRotated;
        Vector2Int itemSize = isRotated ? 
            new Vector2Int(draggedItem.itemData.size.y, draggedItem.itemData.size.x) : 
            draggedItem.itemData.size;
        
        bool canPlace = false;
        bool updateSuccess = false;
        
        if (containerUI != null && containerUI.containerManager != null)
        {
            // Container internal movement
            canPlace = containerUI.containerManager.CanPlaceItem(draggedItem, gridPosition, itemSize);
            if (canPlace)
            {
                updateSuccess = containerUI.containerManager.UpdateItemPosition(
                    draggedItem,
                    gridPosition,
                    itemSize,
                    isRotated
                );
            }
        }
        
        if (canPlace && updateSuccess)
        {
            Debug.Log($"Item moved within container to grid position: {gridPosition}");
        }
        else
        {
            Debug.Log($"Cannot move item within container to grid position: {gridPosition}");
        }
    }
    
    private void HandleHotbarToContainerTransfer(HotbarSlot hotbarSlot)
    {
        InventoryItem draggedItem = hotbarSlot.storedItem;
        if (draggedItem == null) return;
        
        Vector2Int itemSize = draggedItem.GetRotatedSize();
        
        bool canPlace = false;
        bool updateSuccess = false;
        
        if (containerUI != null && containerUI.containerManager != null)
        {
            // Check if container can accept the item
            canPlace = containerUI.containerManager.CanPlaceItem(draggedItem, gridPosition, itemSize);
            if (canPlace)
            {
                // Remove from hotbar
                hotbarSlot.RemoveItem();
                
                // Add to container inventory
                updateSuccess = containerUI.containerManager.TryAddItem(draggedItem, gridPosition, draggedItem.isRotated);
                
                if (!updateSuccess)
                {
                    // If container add failed, put back in hotbar
                    hotbarSlot.SetItem(draggedItem);
                }
            }
        }
        
        if (canPlace && updateSuccess)
        {
            Debug.Log($"Item transferred from hotbar to container at grid position: {gridPosition}");
        }
        else
        {
            Debug.Log($"Cannot transfer item from hotbar to container at grid position: {gridPosition}");
        }
    }
    
    // Used to reset the slot color
    public void ResetColor()
    {
        Color newColor = defaultColor;
        // Maintain the current alpha value when resetting color
        newColor.a = slotImage.color.a;
        slotImage.color = newColor;
    }
}
