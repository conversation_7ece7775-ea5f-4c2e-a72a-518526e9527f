using UnityEngine;

public class InventoryPanelResizer : MonoBehaviour
{
    [Header("Inventory Manager Reference")]
    public InventoryManager inventoryManager;

    private RectTransform border2Rect;
    private RectTransform borderRect;
    private RectTransform backgroundRect;
    private RectTransform screenShaderRect;
    private InventoryUI inventoryUI;

    private int lastWidth = -1;
    private int lastHeight = -1;

    void Awake()
    {
        // Find children by name
        border2Rect = transform.Find("Border2")?.GetComponent<RectTransform>();
        borderRect = transform.Find("Border")?.GetComponent<RectTransform>();
        backgroundRect = transform.Find("Background")?.GetComponent<RectTransform>();
        screenShaderRect = transform.Find("ScreenShader")?.GetComponent<RectTransform>();
        
        // Get InventoryUI component
        inventoryUI = GetComponent<InventoryUI>();
    }

    void Start()
    {
        if (inventoryManager == null)
        {
            inventoryManager = FindObjectOfType<InventoryManager>();
        }
        UpdateRects();
    }

    void Update()
    {
        if (inventoryManager == null) return;
        if (inventoryManager.width != lastWidth || inventoryManager.height != lastHeight)
        {
            UpdateRects();
        }
    }

    void UpdateRects()
    {
        if (inventoryManager == null) return;
        int w = inventoryManager.width;
        int h = inventoryManager.height;
        lastWidth = w;
        lastHeight = h;

        // Set InventoryPanel position
        if (w == 5 && h == 5)
        {
            SetPanelPosition(380, -220);
            SetRect(border2Rect, 250, -250, 620, 620);
            SetRect(borderRect, 250, -250, 600, 600);
            SetRect(backgroundRect, 250, -250, 510, 510);
            SetRect(screenShaderRect, 250, -250, 510, 510);
        }
        else if (w == 6 && h == 6)
        {
            SetPanelPosition(340, -150);
            SetRect(border2Rect, 300, -300, 730, 730);
            SetRect(borderRect, 300, -300, 700, 700);
            SetRect(backgroundRect, 300, -300, 600, 600);
            SetRect(screenShaderRect, 300, -300, 600, 600);
        }
        else if (w == 7 && h == 7)
        {
            SetPanelPosition(240, -100);
            SetRect(border2Rect, 350, -350, 840, 840);
            SetRect(borderRect, 350, -350, 820, 820);
            SetRect(backgroundRect, 350, -350, 700, 700);
            SetRect(screenShaderRect, 350, -350, 700, 700);
        }
    }

    void SetRect(RectTransform rect, float posX, float posY, float width, float height)
    {
        if (rect == null) return;
        rect.anchoredPosition = new Vector2(posX, posY);
        rect.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, width);
        rect.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, height);
    }

    void SetPanelPosition(float posX, float posY)
    {
        var panelRect = GetComponent<RectTransform>();
        if (panelRect != null)
        {
            panelRect.anchoredPosition = new Vector2(posX, posY);
        }
        
        // Update InventoryUI's initialAnchoredPosition to match
        if (inventoryUI != null)
        {
            // Use reflection to access the private field
            var field = typeof(InventoryUI).GetField("initialAnchoredPosition", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                field.SetValue(inventoryUI, new Vector2(posX, posY));
            }
        }
    }
} 