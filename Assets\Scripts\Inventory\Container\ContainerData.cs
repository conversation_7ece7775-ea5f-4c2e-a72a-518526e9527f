using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(fileName = "NewContainerData", menuName = "Inventory/Container Data")]
public class ContainerData : ScriptableObject
{
    [Header("Container Properties")]
    [Tooltip("Display name for the container")]
    public string containerName = "Container";
    
    [Tooltip("Grid size of the container inventory")]
    public int width = 4;
    public int height = 4;
    
    [Head<PERSON>("Starting Items")]
    [Tooltip("Items that spawn in this container when first created")]
    public List<ContainerStartingItem> startingItems = new List<ContainerStartingItem>();
    
    [System.Serializable]
    public class ContainerStartingItem
    {
        public ItemData itemData;
        public Vector2Int position;
        public bool isRotated = false;
        [Tooltip("For stackable items like ammo")]
        public int stackCount = 1;
        [Tooltip("For weapons - current ammo in magazine")]
        public int currentAmmo = 0;
    }
}
