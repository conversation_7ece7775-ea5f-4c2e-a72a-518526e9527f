%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &6896631203371644850
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3955320676221070467}
  - component: {fileID: 1702950296741517284}
  - component: {fileID: 2695144016371957039}
  - component: {fileID: 1529516408021289445}
  - component: {fileID: 6872172135973823780}
  - component: {fileID: -4506446195712771982}
  - component: {fileID: 2659528207556587274}
  m_Layer: 5
  m_Name: ItemUI
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3955320676221070467
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6896631203371644850}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -50, y: 50}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0, y: 1}
--- !u!222 &1702950296741517284
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6896631203371644850}
  m_CullTransparentMesh: 1
--- !u!114 &2695144016371957039
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6896631203371644850}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &1529516408021289445
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6896631203371644850}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a8b0644eb6d3c149a356278422aee2a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemData: {fileID: 0}
  itemImage: {fileID: 0}
  rectTransform: {fileID: 0}
  positionInGrid: {x: 0, y: 0}
  isRotated: 0
  stackCountText: {fileID: 0}
  ammoCountText: {fileID: 0}
  customFont: {fileID: 0}
  canvas: {fileID: 0}
  moveSpeed: 15
  movementCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  tiltFactor: 15
  tiltSmoothing: 5
  dragResistance: 8
  maxTiltVelocity: 1000
  rotationMomentumFactor: 0.5
  minRotationSpeed: 360
  maxRotationSpeed: 720
--- !u!225 &6872172135973823780
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6896631203371644850}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &-4506446195712771982
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6896631203371644850}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f1b0e78bd6ad524dbb906581abbcb27, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  temporaryIsRotated: 0
  validPlacementColor: {r: 0.3840584, g: 0.6301887, b: 0.47836998, a: 0.2}
  invalidPlacementColor: {r: 0.5999999, g: 0.08037735, b: 0.08037735, a: 0.29411766}
  selectedOutlineColor: {r: 1, g: 1, b: 1, a: 1}
  dragAlpha: 0.8
  validDragTint: {r: 1, g: 1, b: 1, a: 0.6}
  invalidDragTint: {r: 1, g: 0.5, b: 0.5, a: 0.6}
  outlineWidth: 1.68
  backendItem: {fileID: 0}
  playerTransform: {fileID: 0}
  dropOffset: {x: 0, y: 0, z: 0}
--- !u!114 &2659528207556587274
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6896631203371644850}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 776848c99bdf35e45b6dd470a27f619c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  temporaryIsRotated: 0
  validPlacementColor: {r: 0.5, g: 1, b: 0.5, a: 0.5}
  invalidPlacementColor: {r: 1, g: 0.5, b: 0.5, a: 0.5}
  dragAlpha: 0.6
  validDragTint: {r: 1, g: 1, b: 1, a: 0.6}
  invalidDragTint: {r: 1, g: 0.5, b: 0.5, a: 0.6}
  backendItem: {fileID: 0}
