using UnityEngine;
using UnityEngine.UI;
using System.Collections;

/// <summary>
/// Manages the player's hotbar UI, allowing quick access to inventory items.
/// </summary>
public class Hotbar : MonoBehaviour
{
    [Header("References")]
    [Tooltip("Prefab used for each hotbar slot")]
    public GameObject slotPrefab;

    [Toolt<PERSON>("Parent transform that holds all slot UI elements")]
    public Transform slotContainer;

    [Toolt<PERSON>("Parent transform for any item visuals")]
    public Transform itemContainer;

    [Header("Configuration")]
    [Tooltip("Number of slots to create in the hotbar")]
    [Range(1, 10)]
    public int numberOfSlots = 3;

    [Header("Slot Scaling")]
    [SerializeField] private float inventoryOpenScale = 1.15f;
    [SerializeField] private float scaleAnimDuration = 0.2f;
    [SerializeField] private AnimationCurve scaleCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

    [Header("Slot Spacing")]
    [Tooltip("Space between slots in pixels when inventory is closed")]
    [SerializeField] private float normalSpacing = 30f;
    [<PERSON>lt<PERSON>("Additional spacing when inventory is open")]
    [SerializeField] private float inventoryOpenExtraSpacing = 25f;

    [Tooltip("Size of each slot in pixels")]
    private const float SLOT_SIZE = 100f;

    [Tooltip("Distance from bottom of screen in pixels")]
    private const float BOTTOM_OFFSET = 70f;


    // References
    public HotbarSlot[] hotbarSlots { get; private set; }
    public bool canReceiveItems { get; private set; }
    public InventoryUI inventoryUI { get; private set; }
    public InventoryManager inventoryManager { get; private set; }

    // Layout references
    private HorizontalLayoutGroup layoutGroup;
    private RectTransform containerRect;

    // Selection state
    private int selectedSlotIndex = 0;
    private int lastSelectedSlotIndex = 0;
    private bool wasInventoryOpen = false;

    private void Awake()
    {
        // Find inventory references
        FindInventoryReferences();
    }

    private void Start()
    {
        InitializeHotbar();
        SetCanReceiveItems(true);

        // Wait a frame to let Unity finish UI initialization
        StartCoroutine(DelayedLayoutRefresh());

        // Select first slot by default
        SelectSlot(0);
    }

    /// <summary>
    /// Initializes the hotbar by creating slots and setting up layout.
    /// </summary>
    private void InitializeHotbar()
    {
        // Initialize the array
        hotbarSlots = new HotbarSlot[numberOfSlots];

        // Calculate total width and setup the container layout
        SetupContainerLayout();

        // Create all slots
        CreateHotbarSlots();
    }

    /// <summary>
    /// Sets up the container layout group and positioning
    /// </summary>
    private void SetupContainerLayout()
    {
        float totalWidth = (SLOT_SIZE * numberOfSlots) + (normalSpacing * (numberOfSlots - 1));

        // Set up horizontal layout group
        layoutGroup = slotContainer.GetComponent<HorizontalLayoutGroup>();
        if (layoutGroup == null)
        {
            layoutGroup = slotContainer.gameObject.AddComponent<HorizontalLayoutGroup>();
        }

        // Configure layout properties
        layoutGroup.childAlignment = TextAnchor.MiddleCenter;
        layoutGroup.spacing = normalSpacing;
        layoutGroup.childForceExpandWidth = false;
        layoutGroup.childForceExpandHeight = false;
        layoutGroup.childControlWidth = false;
        layoutGroup.childControlHeight = false;

        // Position container at bottom center
        containerRect = slotContainer.GetComponent<RectTransform>();
        containerRect.anchorMin = new Vector2(0.5f, 0);
        containerRect.anchorMax = new Vector2(0.5f, 0);
        containerRect.pivot = new Vector2(0.5f, 0);
        containerRect.anchoredPosition = new Vector2(0, BOTTOM_OFFSET);
        containerRect.sizeDelta = new Vector2(totalWidth, SLOT_SIZE);
    }

    /// <summary>
    /// Creates all hotbar slot GameObjects
    /// </summary>
    private void CreateHotbarSlots()
    {
        for (int i = 0; i < numberOfSlots; i++)
        {
            GameObject newSlot = Instantiate(slotPrefab, slotContainer);

            // Configure slot appearance
            RectTransform rectTransform = newSlot.GetComponent<RectTransform>();
            rectTransform.sizeDelta = new Vector2(SLOT_SIZE, SLOT_SIZE);

            // Ensure slot background is visible
            Image slotImage = newSlot.GetComponent<Image>();
            slotImage.color = Color.white;

            // Get or add hotbar slot component
            HotbarSlot slot = newSlot.GetComponent<HotbarSlot>() ?? newSlot.AddComponent<HotbarSlot>();
            hotbarSlots[i] = slot;
        }
    }

    /// <summary>
    /// Finds references to inventory systems in the scene
    /// </summary>
    private void FindInventoryReferences()
    {
        inventoryUI = FindObjectOfType<InventoryUI>();
        if (inventoryUI != null)
        {
            inventoryManager = inventoryUI.inventoryManager;
            Debug.Log("Hotbar found InventoryUI reference");
        }
        else
        {
            Debug.LogWarning("Hotbar could not find InventoryUI in scene!");
        }
    }

    /// <summary>
    /// Wait a frame then refresh the layout to ensure proper positioning
    /// </summary>
    private IEnumerator DelayedLayoutRefresh()
    {
        yield return null; // Wait one frame
        RefreshLayout();
    }

    private void Update()
    {
        bool isInventoryOpen = inventoryUI != null && inventoryUI.gameObject.activeInHierarchy;

        // Handle inventory state change
        if (wasInventoryOpen != isInventoryOpen)
        {
            if (isInventoryOpen)
            {
                // Inventory just opened - store current selection and expand all slots
                lastSelectedSlotIndex = selectedSlotIndex;
                DeselectCurrentSlot();
                StartCoroutine(AnimateAllSlots(true));
            }
            else
            {
                // Inventory just closed - restore last selection and shrink non-selected slots
                StartCoroutine(AnimateAllSlots(false, lastSelectedSlotIndex));
                SelectSlot(lastSelectedSlotIndex);
            }
            wasInventoryOpen = isInventoryOpen;
        }

        // Only handle navigation when inventory is closed
        if (!isInventoryOpen)
        {
            // Handle number key input
            for (int i = 0; i < hotbarSlots.Length; i++)
            {
                if (Input.GetKeyDown(KeyCode.Alpha1 + i))
                {
                    SelectSlot(i);
                    UseHotbarItem(i);
                }
            }

            // Handle scroll wheel input
            float scroll = Input.GetAxis("Mouse ScrollWheel");
            if (Mathf.Abs(scroll) > 0.01f)
            {
                int direction = scroll > 0 ? -1 : 1;
                int newIndex = selectedSlotIndex + direction;

                // Wrap around
                if (newIndex < 0) newIndex = hotbarSlots.Length - 1;
                if (newIndex >= hotbarSlots.Length) newIndex = 0;

                SelectSlot(newIndex);
            }

            // Handle G key for dropping selected item
            if (Input.GetKeyDown(KeyCode.G))
            {
                DropSelectedItem();
            }
        }
    }

    private void SelectSlot(int index)
    {
        if (index < 0 || index >= hotbarSlots.Length) return;

        // Deselect current slot
        DeselectCurrentSlot();

        // Select new slot
        selectedSlotIndex = index;
        hotbarSlots[selectedSlotIndex].Select();
    }

    private void DeselectCurrentSlot()
    {
        if (selectedSlotIndex >= 0 && selectedSlotIndex < hotbarSlots.Length)
        {
            hotbarSlots[selectedSlotIndex].Deselect();
        }
    }

    private void OnEnable()
    {
        SetCanReceiveItems(true);
    }

    private void OnDisable()
    {
        SetCanReceiveItems(false);
    }

    /// <summary>
    /// Sets whether the hotbar can receive new items
    /// </summary>
    public void SetCanReceiveItems(bool canReceive)
    {
        canReceiveItems = canReceive;
    }

    /// <summary>
    /// Gets the currently selected hotbar slot
    /// </summary>
    /// <returns>The selected HotbarSlot, or null if none selected</returns>
    public HotbarSlot GetSelectedSlot()
    {
        if (selectedSlotIndex >= 0 && selectedSlotIndex < hotbarSlots.Length)
        {
            return hotbarSlots[selectedSlotIndex];
        }
        return null;
    }

    /// <summary>
    /// Attempts to add an item to a specific hotbar slot
    /// </summary>
    /// <returns>True if successful, false otherwise</returns>
    public bool TryAddItemToSlot(InventoryItem item, int slotIndex)
    {
        if (!canReceiveItems || slotIndex < 0 || slotIndex >= hotbarSlots.Length ||
            hotbarSlots[slotIndex].storedItem != null)
            return false;

        hotbarSlots[slotIndex].SetItem(item);
        return true;
    }

    /// <summary>
    /// Attempts to add an item to the first empty hotbar slot
    /// </summary>
    /// <returns>True if successful, false if no empty slots</returns>
    public bool TryAddItemToFirstEmptySlot(InventoryItem item)
    {
        if (!canReceiveItems || item == null)
            return false;

        for (int i = 0; i < hotbarSlots.Length; i++)
        {
            if (hotbarSlots[i].storedItem == null)
            {
                hotbarSlots[i].SetItem(item);
                return true;
            }
        }

        return false; // No empty slots found
    }

    /// <summary>
    /// Uses the item in the specified hotbar slot
    /// </summary>
    private void UseHotbarItem(int slotIndex)
    {
        if (slotIndex < 0 || slotIndex >= hotbarSlots.Length)
            return;

        HotbarSlot slot = hotbarSlots[slotIndex];
        if (slot.storedItem != null)
        {
            slot.UseItem();
        }
    }

    /// <summary>
    /// Drops the item from the currently selected hotbar slot
    /// </summary>
    private void DropSelectedItem()
    {
        // Check if we have a valid selected slot
        if (selectedSlotIndex < 0 || selectedSlotIndex >= hotbarSlots.Length)
        {
            Debug.Log("No hotbar slot selected for dropping");
            return;
        }

        HotbarSlot selectedSlot = hotbarSlots[selectedSlotIndex];
        if (selectedSlot.storedItem == null)
        {
            Debug.Log("Selected hotbar slot is empty");
            return;
        }

        // Get item name for feedback
        string itemName = selectedSlot.storedItem.GetName();

        // Drop the item from the selected slot
        selectedSlot.DropItem();

        // Provide user feedback
        Debug.Log($"Dropped {itemName} from hotbar slot {selectedSlotIndex + 1}");
    }

    /// <summary>
    /// Returns all items from hotbar to inventory, typically when closing
    /// </summary>
    public void ReturnItemsToInventory()
    {
        if (inventoryManager == null)
        {
            Debug.LogError("Cannot return items to inventory: missing inventory reference");
            return;
        }

        foreach (HotbarSlot slot in hotbarSlots)
        {
            if (slot.storedItem == null)
                continue;

            InventoryItem item = slot.storedItem;
            bool success = inventoryManager.PickupItem(item);

            if (success)
            {
                slot.SetItem(null);
            }
            else
            {
                Debug.LogWarning($"Could not return {item.itemData.itemName} to inventory - no space!");
                // Could implement item dropping here if needed
            }
        }
    }

    /// <summary>
    /// Forces a refresh of the hotbar layout
    /// </summary>
    public void RefreshLayout()
    {
        HorizontalLayoutGroup layoutGroup = slotContainer.GetComponent<HorizontalLayoutGroup>();
        if (layoutGroup == null)
            return;

        // Toggle to force update
        layoutGroup.enabled = false;
        layoutGroup.enabled = true;

        // Recalculate container size
        float totalWidth = (SLOT_SIZE * numberOfSlots) + (normalSpacing * (numberOfSlots - 1));

        RectTransform containerRect = slotContainer.GetComponent<RectTransform>();
        containerRect.sizeDelta = new Vector2(totalWidth, SLOT_SIZE);

        // Force immediate layout rebuild
        Canvas.ForceUpdateCanvases();
    }

    /// <summary>
    /// Debug method to test adding an item to the hotbar
    /// </summary>
    [ContextMenu("Test Add Item")]
    public void TestAddItem()
    {
        InventoryItem[] allItems = FindObjectsOfType<InventoryItem>();
        if (allItems.Length > 0 && allItems[0].itemData != null)
        {
            TryAddItemToSlot(allItems[0], 0);
        }
        else
        {
            Debug.LogError("No items found to test with");
        }
    }

    private IEnumerator AnimateAllSlots(bool expanding, int selectedIndex = -1)
    {
        float elapsedTime = 0f;

        // Store starting values
        Vector3[] startScales = new Vector3[hotbarSlots.Length];
        float startSpacing = layoutGroup.spacing;
        float targetSpacing = expanding ? normalSpacing + inventoryOpenExtraSpacing : normalSpacing;

        for (int i = 0; i < hotbarSlots.Length; i++)
        {
            if (hotbarSlots[i] != null)
            {
                startScales[i] = hotbarSlots[i].transform.localScale;
            }
        }

        // Calculate target scales
        Vector3[] targetScales = new Vector3[hotbarSlots.Length];
        for (int i = 0; i < hotbarSlots.Length; i++)
        {
            if (hotbarSlots[i] != null)
            {
                if (expanding)
                {
                    targetScales[i] = Vector3.one * inventoryOpenScale;
                }
                else
                {
                    // When closing, only the selected slot stays scaled
                    targetScales[i] = (i == selectedIndex) ? Vector3.one * 1.2f : Vector3.one;
                }
            }
        }

        // Calculate container widths
        float startWidth = containerRect.sizeDelta.x;
        float targetWidth = (SLOT_SIZE * numberOfSlots * (expanding ? inventoryOpenScale : 1f)) +
                           (targetSpacing * (numberOfSlots - 1));

        // Animate
        while (elapsedTime < scaleAnimDuration)
        {
            float t = elapsedTime / scaleAnimDuration;
            t = scaleCurve.Evaluate(t);

            // Update scales
            for (int i = 0; i < hotbarSlots.Length; i++)
            {
                if (hotbarSlots[i] != null)
                {
                    hotbarSlots[i].transform.localScale = Vector3.Lerp(startScales[i], targetScales[i], t);
                }
            }

            // Update spacing and container width
            layoutGroup.spacing = Mathf.Lerp(startSpacing, targetSpacing, t);
            containerRect.sizeDelta = new Vector2(
                Mathf.Lerp(startWidth, targetWidth, t),
                SLOT_SIZE
            );

            elapsedTime += Time.deltaTime;
            yield return null;
        }

        // Set final values
        for (int i = 0; i < hotbarSlots.Length; i++)
        {
            if (hotbarSlots[i] != null)
            {
                hotbarSlots[i].transform.localScale = targetScales[i];
            }
        }
        layoutGroup.spacing = targetSpacing;
        containerRect.sizeDelta = new Vector2(targetWidth, SLOT_SIZE);
    }
}