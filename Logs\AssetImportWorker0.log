Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.33f1 (b2c853adf198) revision 11716691'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'en' Physical Memory: 16087 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.33f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Users/<USER>/Hydra Games/Void Voyager
-logFile
Logs/AssetImportWorker0.log
-srvPort
65324
Successfully changed project path to: C:/Users/<USER>/Hydra Games/Void Voyager
C:/Users/<USER>/Hydra Games/Void Voyager
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [87368] Host "[IP] ************* [Port] 0 [Flags] 2 [Guid] 209462736 [EditorId] 209462736 [Version] 1048832 [Id] WindowsEditor(7,MSI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [87368] Host "[IP] ************* [Port] 0 [Flags] 2 [Guid] 209462736 [EditorId] 209462736 [Version] 1048832 [Id] WindowsEditor(7,MSI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
Refreshing native plugins compatible for Editor in 569.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.33f1 (b2c853adf198)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Hydra Games/Void Voyager/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2820)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.7652
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56752
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.009015 seconds.
- Loaded All Assemblies, in  0.524 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.224 seconds
Domain Reload Profiling: 747ms
	BeginReloadAssembly (115ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (324ms)
		LoadAssemblies (113ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (322ms)
			TypeCache.Refresh (321ms)
				TypeCache.ScanAssembly (310ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (225ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (186ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (140ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  5.761 seconds
Refreshing native plugins compatible for Editor in 5.50 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.642 seconds
Domain Reload Profiling: 6401ms
	BeginReloadAssembly (114ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (18ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (5582ms)
		LoadAssemblies (5233ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (412ms)
			TypeCache.Refresh (373ms)
				TypeCache.ScanAssembly (202ms)
			ScanForSourceGeneratedMonoScriptInfo (26ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (642ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (486ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (332ms)
			ProcessInitializeOnLoadMethodAttributes (61ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.08 seconds
Refreshing native plugins compatible for Editor in 5.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5802 Unused Serialized files (Serialized files now loaded: 0)
Unloading 87 unused Assets / (351.0 KB). Loaded Objects now: 6260.
Memory consumption went from 225.3 MB to 225.0 MB.
Total: 4.281500 ms (FindLiveObjects: 0.368200 ms CreateObjectMapping: 0.154900 ms MarkObjects: 3.502800 ms  DeleteObjects: 0.254500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.947 seconds
Refreshing native plugins compatible for Editor in 5.93 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 27.177 seconds
Domain Reload Profiling: 28121ms
	BeginReloadAssembly (308ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (50ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (121ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (567ms)
		LoadAssemblies (630ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (25ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (27177ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (451ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (297ms)
			ProcessInitializeOnLoadMethodAttributes (61ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 5.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (321.7 KB). Loaded Objects now: 6266.
Memory consumption went from 195.2 MB to 194.9 MB.
Total: 4.952900 ms (FindLiveObjects: 0.337400 ms CreateObjectMapping: 0.136300 ms MarkObjects: 4.370400 ms  DeleteObjects: 0.107100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  4.603 seconds
Refreshing native plugins compatible for Editor in 5.01 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 53.712 seconds
Domain Reload Profiling: 58313ms
	BeginReloadAssembly (279ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (100ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (4253ms)
		LoadAssemblies (4334ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (26ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (53713ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (462ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (302ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 4.78 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (321.8 KB). Loaded Objects now: 6271.
Memory consumption went from 195.2 MB to 194.9 MB.
Total: 5.056400 ms (FindLiveObjects: 0.267800 ms CreateObjectMapping: 0.125600 ms MarkObjects: 4.572100 ms  DeleteObjects: 0.090100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.356 seconds
Refreshing native plugins compatible for Editor in 4.27 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.974 seconds
Domain Reload Profiling: 1327ms
	BeginReloadAssembly (123ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (180ms)
		LoadAssemblies (231ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (13ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (974ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (402ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (268ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 7.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (321.8 KB). Loaded Objects now: 6276.
Memory consumption went from 195.2 MB to 194.9 MB.
Total: 4.681500 ms (FindLiveObjects: 0.445800 ms CreateObjectMapping: 0.284500 ms MarkObjects: 3.658700 ms  DeleteObjects: 0.291200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.350 seconds
Refreshing native plugins compatible for Editor in 4.97 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.021 seconds
Domain Reload Profiling: 1368ms
	BeginReloadAssembly (116ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (186ms)
		LoadAssemblies (232ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1022ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (409ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (272ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 8.50 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (321.9 KB). Loaded Objects now: 6281.
Memory consumption went from 195.2 MB to 194.9 MB.
Total: 4.249600 ms (FindLiveObjects: 0.376400 ms CreateObjectMapping: 0.230200 ms MarkObjects: 3.475500 ms  DeleteObjects: 0.166000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 888812.724523 seconds.
  path: Assets/Prefabs/Player 1.prefab
  artifactKey: Guid(511b0ef628f3601458890f7e0cfe155f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Player 1.prefab using Guid(511b0ef628f3601458890f7e0cfe155f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '3436f877b4dfeef1456d7b8062629fe4') in 0.430662 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 239
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.381 seconds
Refreshing native plugins compatible for Editor in 3.99 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.057 seconds
Domain Reload Profiling: 1435ms
	BeginReloadAssembly (134ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (194ms)
		LoadAssemblies (242ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (15ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1057ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (431ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (72ms)
			ProcessInitializeOnLoadAttributes (287ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 8.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (321.8 KB). Loaded Objects now: 6333.
Memory consumption went from 199.9 MB to 199.6 MB.
Total: 4.476800 ms (FindLiveObjects: 0.427700 ms CreateObjectMapping: 0.265400 ms MarkObjects: 3.632000 ms  DeleteObjects: 0.150000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 699.117078 seconds.
  path: Assets/Scripts/Inventory/GridSlot.prefab
  artifactKey: Guid(e1b255c78a9479441a157180f03cbdae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/GridSlot.prefab using Guid(e1b255c78a9479441a157180f03cbdae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '10abc8d02de0d7dd4c9ea9ad16d8c66a') in 0.227424 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 10
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  4.767 seconds
Refreshing native plugins compatible for Editor in 3.96 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.781 seconds
Domain Reload Profiling: 6546ms
	BeginReloadAssembly (200ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (4509ms)
		LoadAssemblies (4566ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (19ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1782ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (360ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (240ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 7.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (321.8 KB). Loaded Objects now: 6338.
Memory consumption went from 199.9 MB to 199.6 MB.
Total: 8.085500 ms (FindLiveObjects: 0.900200 ms CreateObjectMapping: 0.201300 ms MarkObjects: 6.829900 ms  DeleteObjects: 0.152600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  6.071 seconds
Refreshing native plugins compatible for Editor in 3.71 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.390 seconds
Domain Reload Profiling: 7460ms
	BeginReloadAssembly (661ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (120ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (338ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (5355ms)
		LoadAssemblies (5381ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (50ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (19ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1391ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (363ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (60ms)
			ProcessInitializeOnLoadAttributes (241ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 5.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5641 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (321.9 KB). Loaded Objects now: 6350.
Memory consumption went from 200.0 MB to 199.7 MB.
Total: 6.675600 ms (FindLiveObjects: 0.722700 ms CreateObjectMapping: 0.224100 ms MarkObjects: 5.566900 ms  DeleteObjects: 0.160600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1303.899552 seconds.
  path: Assets/Scripts/Inventory/ItemUI.prefab
  artifactKey: Guid(f3a3538ab54d39c47a3038f653f40731) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/ItemUI.prefab using Guid(f3a3538ab54d39c47a3038f653f40731) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '47cc00c83d79a2514833fb1b33d9dcbc') in 0.037763 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 9
========================================================================
Received Import Request.
  Time since last request: 1127.779109 seconds.
  path: Assets/Scripts/Inventory
  artifactKey: Guid(aac23ca8afdf7744a8952dd131248469) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory using Guid(aac23ca8afdf7744a8952dd131248469) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'd3c78c38b1243362ab5a383d85ef8203') in 0.004533 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 8.386803 seconds.
  path: Assets/Scripts/Inventory/Container
  artifactKey: Guid(4a395b519928f014c817a9b9244345b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Container using Guid(4a395b519928f014c817a9b9244345b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'b47426c696a6fd28534225cefe95f42a') in 0.000977 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.922684 seconds.
  path: Assets/Scripts/Inventory/ContainerData.cs
  artifactKey: Guid(c391477c09cf268408aae70745a7c093) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/ContainerData.cs using Guid(c391477c09cf268408aae70745a7c093) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '24a85a20ae2960f7d1d1aaf84cb615bf') in 0.001018 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 4.706500 seconds.
  path: Assets/Scripts/Inventory/DualInventoryManager.cs
  artifactKey: Guid(5c70ce5a433922849b92029168aca097) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/DualInventoryManager.cs using Guid(5c70ce5a433922849b92029168aca097) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '51aa05724cc28a5478f3699e1557707b') in 0.001413 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.473612 seconds.
  path: Assets/Scripts/Inventory/ContainerInventoryUI.cs
  artifactKey: Guid(7747d4beb12751e4d82c3f98b6792d03) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/ContainerInventoryUI.cs using Guid(7747d4beb12751e4d82c3f98b6792d03) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '7c6deff9be0982ea96d974cc20c4a223') in 0.001295 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.586016 seconds.
  path: Assets/Scripts/Inventory/ContainerInteractable.cs
  artifactKey: Guid(19d0cd3bb0396c049835911f9e96a900) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/ContainerInteractable.cs using Guid(19d0cd3bb0396c049835911f9e96a900) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '47616f2d1c668cac316b0a143a477c7e') in 0.001230 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.961701 seconds.
  path: Assets/Scripts/Inventory/ContainerInventoryItemUIDrag.cs
  artifactKey: Guid(776848c99bdf35e45b6dd470a27f619c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/ContainerInventoryItemUIDrag.cs using Guid(776848c99bdf35e45b6dd470a27f619c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'fb4f8b08f010db0a90c174aef4aac425') in 0.001239 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2.215033 seconds.
  path: Assets/Scripts/Inventory/ContainerInventoryManager.cs
  artifactKey: Guid(8fe6becde4560e049bd8340bdfb20b65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/ContainerInventoryManager.cs using Guid(8fe6becde4560e049bd8340bdfb20b65) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'abc59d9dd4fbb678a92ea7640e9a1ff8') in 0.000801 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.814046 seconds.
  path: Assets/Scripts/Inventory/ContainerInventorySlot.cs
  artifactKey: Guid(5d46e73df932e3b4e8e767a8fb34c310) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/ContainerInventorySlot.cs using Guid(5d46e73df932e3b4e8e767a8fb34c310) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'ff122a6f9bc3cd381c5f36869b9a03f3') in 0.000799 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  5.693 seconds
Refreshing native plugins compatible for Editor in 4.27 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 12.934 seconds
Domain Reload Profiling: 18625ms
	BeginReloadAssembly (287ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (111ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (5339ms)
		LoadAssemblies (5395ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (49ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (24ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (12934ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (403ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (62ms)
			ProcessInitializeOnLoadAttributes (273ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 4.64 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5641 Unused Serialized files (Serialized files now loaded: 0)
Unloading 50 unused Assets / (321.4 KB). Loaded Objects now: 6355.
Memory consumption went from 200.0 MB to 199.7 MB.
Total: 5.371400 ms (FindLiveObjects: 0.305900 ms CreateObjectMapping: 0.129900 ms MarkObjects: 4.824100 ms  DeleteObjects: 0.110500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 61.967015 seconds.
  path: Assets/Scripts/Inventory/Container/ContainerInventoryUI.cs
  artifactKey: Guid(7747d4beb12751e4d82c3f98b6792d03) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Container/ContainerInventoryUI.cs using Guid(7747d4beb12751e4d82c3f98b6792d03) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '13449de82b2d3f363fc970e551bd867d') in 0.020925 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 137.386162 seconds.
  path: Assets/Scripts/Inventory/Container/ContainerInteractable.cs
  artifactKey: Guid(19d0cd3bb0396c049835911f9e96a900) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Container/ContainerInteractable.cs using Guid(19d0cd3bb0396c049835911f9e96a900) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'dca161913a53352fe2a242deb862eb2f') in 0.001635 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2.383781 seconds.
  path: Assets/Scripts/Inventory/Container/ContainerInventoryItemUIDrag.cs
  artifactKey: Guid(776848c99bdf35e45b6dd470a27f619c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Container/ContainerInventoryItemUIDrag.cs using Guid(776848c99bdf35e45b6dd470a27f619c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '1dd368d59e21597e2f398963f37da50a') in 0.000737 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.314908 seconds.
  path: Assets/Scripts/Inventory/Container/ContainerInventoryManager.cs
  artifactKey: Guid(8fe6becde4560e049bd8340bdfb20b65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Container/ContainerInventoryManager.cs using Guid(8fe6becde4560e049bd8340bdfb20b65) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '0e9363766be80d9a781132dbc9449466') in 0.000607 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.311875 seconds.
  path: Assets/Scripts/Inventory/Container/ContainerInventorySlot.cs
  artifactKey: Guid(5d46e73df932e3b4e8e767a8fb34c310) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Container/ContainerInventorySlot.cs using Guid(5d46e73df932e3b4e8e767a8fb34c310) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'd1ae0d72f96fa1b0b84f0a2c8fcc0994') in 0.000631 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.576329 seconds.
  path: Assets/Scripts/Inventory/Container/ContainerData.cs
  artifactKey: Guid(c391477c09cf268408aae70745a7c093) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Container/ContainerData.cs using Guid(c391477c09cf268408aae70745a7c093) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '63688d36fb0f064c84025296f0641dbe') in 0.000576 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 4054.973644 seconds.
  path: Assets/Scripts/Inventory/Container/Box.asset
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Container/Box.asset using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '6514b74c3147e6bf068c5a783b3ac973') in 0.258393 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 213.789761 seconds.
  path: Assets/Scripts/Inventory/ItemUI.prefab
  artifactKey: Guid(f3a3538ab54d39c47a3038f653f40731) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/ItemUI.prefab using Guid(f3a3538ab54d39c47a3038f653f40731) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'b51c822e98d8003786c5e68a573e3bd9') in 0.215237 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 10
========================================================================
Received Import Request.
  Time since last request: 94.259574 seconds.
  path: Assets/Scripts/Inventory/Items
  artifactKey: Guid(9841cdf445db1c2479c2d0655b1fbdbf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Items using Guid(9841cdf445db1c2479c2d0655b1fbdbf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '6ac94d9ed472d3f568e4ee41ff5e2678') in 0.002217 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.723700 seconds.
  path: Assets/Scripts/Inventory/Items/Grenade_Weap.asset
  artifactKey: Guid(2ae92a7d83ce0f44ea45a623a9f9a07d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Items/Grenade_Weap.asset using Guid(2ae92a7d83ce0f44ea45a623a9f9a07d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '91fed9fd6e544b16a3f27100dd492d9c') in 0.052768 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Scripts/Inventory/Items/Rifle_Weap.asset
  artifactKey: Guid(cb1097cbae4f3d44faa421e99953cfaf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Items/Rifle_Weap.asset using Guid(cb1097cbae4f3d44faa421e99953cfaf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'c41fc69831400842ba1610b4de6d553c') in 0.057615 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Scripts/Inventory/Items/Rifle_Ammo.asset
  artifactKey: Guid(6c9a396faa2e6504e9af103ad1233233) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Items/Rifle_Ammo.asset using Guid(6c9a396faa2e6504e9af103ad1233233) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '4a086d90b37707029b2cbfee28521dab') in 0.055521 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Scripts/Inventory/Items/Pistol_Weap.asset
  artifactKey: Guid(db775aa3ec91e7b4ca2e45c81076ee0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Items/Pistol_Weap.asset using Guid(db775aa3ec91e7b4ca2e45c81076ee0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '274d5a2d93867411700102526b997abe') in 0.041430 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/Scripts/Inventory/Items/Shotgun_Weap.asset
  artifactKey: Guid(6b8d1cce4881a164c952f740a5f48649) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Items/Shotgun_Weap.asset using Guid(6b8d1cce4881a164c952f740a5f48649) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '01cd9aaa494f7c968796a6d24f18c1e4') in 0.045011 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 14.951932 seconds.
  path: Assets/Scripts/Inventory/GridSlot.prefab
  artifactKey: Guid(e1b255c78a9479441a157180f03cbdae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/GridSlot.prefab using Guid(e1b255c78a9479441a157180f03cbdae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '1ca8027980b9944ff0d0428e3cae7f1d') in 0.020279 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 11
