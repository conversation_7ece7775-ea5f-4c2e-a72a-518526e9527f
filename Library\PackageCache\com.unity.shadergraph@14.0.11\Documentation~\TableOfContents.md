* [About Shader Graph](index)
* [Getting started with Shader Graph](Getting-Started)
    * [Creating a new Shader Graph Asset](Create-Shader-Graph)
    * [My first Shader Graph](First-Shader-Graph)
    * [Shader Graph Window](Shader-Graph-Window)
        * [Blackboard](Blackboard)
        * [Main Preview](Main-Preview)
        * [Graph Inspector](Internal-Inspector.md)
    * [Create Node Menu](Create-Node-Menu)
    * [Graph Settings Tab](Graph-Settings-Tab.md)
    * [Master Stack](Master-Stack)
    * [Sticky Notes](Sticky-Notes)
    * [Sub Graph](Sub-graph)
    * [Color Modes](Color-Modes)
    * [Precision Modes](Precision-Modes)
    * [Preview Mode Control](Preview-Mode-Control)
    * [Custom Function Node](Custom-Function-Node)
    * [Shader Graph Preferences](Shader-Graph-Preferences)
    * [Samples](ShaderGraph-Samples.md)
        * [Feature Examples](Shader-Graph-Sample-Feature-Examples.md)
        * [Production Ready Shaders](Shader-Graph-Sample-Production-Ready.md)
            * [Lit Shaders](Shader-Graph-Sample-Production-Ready-Lit.md)
            * [Decal shaders](Shader-Graph-Sample-Production-Ready-Decal.md)
            * [Detail shaders](Shader-Graph-Sample-Production-Ready-Detail.md)
            * [Rock shaders](Shader-Graph-Sample-Production-Ready-Rock.md)
            * [Water shaders](Shader-Graph-Sample-Production-Ready-Water.md)
            * [Post-process shaders](Shader-Graph-Sample-Production-Ready-Post.md)
            * [Weather shaders](Shader-Graph-Sample-Production-Ready-Weather.md)
            * [Miscellaneous shaders](Shader-Graph-Sample-Production-Ready-Misc.md)
            * [Forest Stream Construction Tutorial ](Shader-Graph-Sample-Production-Ready-Tutorial.md)    
    * [Material Variants](materialvariant-SG)
* Upgrade Guides
  * [Upgrade to Shader Graph 10.0.x](Upgrade-Guide-10-0-x)
* Inside Shader Graph
    * [Shader Graph Asset](Shader-Graph-Asset)
    * [Graph Target](Graph-Target.md)
    * [Sub Graph Asset](Sub-graph-Asset)
        * [SpeedTree 8 Sub Graph Assets](SpeedTree8-SubGraphAssets)
    * [Node](Node)
        * [Port](Port)
        * [Custom Port Menu](Custom-Port-Menu)
        * [Edge](Edge)
    * [Property Types](Property-Types)
    * [Keywords](Keywords)
    * [Data Types](Data-Types)
    * [Port Bindings](Port-Bindings)
    * [Shader Stage](Shader-Stage)
    * [Surface options](surface-options.md)
    * [Custom Interpolators](Custom-Interpolators)
* [Node Library](Node-Library)
    * [Artistic](Artistic-Nodes)
        * Adjustment
            * [Channel Mixer](Channel-Mixer-Node)
            * [Contrast](Contrast-Node)
            * [Hue](Hue-Node)
            * [Invert Colors](Invert-Colors-Node)
            * [Replace Color](Replace-Color-Node)
            * [Saturation](Saturation-Node)
            * [White Balance](White-Balance-Node)
        * Blend
            * [Blend](Blend-Node)
        * Filter
            * [Dither](Dither-Node)
            * [Fade Transition](Fade-Transition-Node)
        * Mask
            * [Channel Mask](Channel-Mask-Node)
            * [Color Mask](Color-Mask-Node)
        * Normal
            * [Normal Blend](Normal-Blend-Node)
            * [Normal From Height](Normal-From-Height-Node)
            * [Normal From Texture](Normal-From-Texture-Node)
            * [Normal Reconstruct Z](Normal-Reconstruct-Z-Node)
            * [Normal Strength](Normal-Strength-Node)
            * [Normal Unpack](Normal-Unpack-Node)
        * Utility
            * [Colorspace Conversion](Colorspace-Conversion-Node)
    * [Channel](Channel-Nodes)
        * [Combine](Combine-Node)
        * [Flip](Flip-Node)
        * [Split](Split-Node)
        * [Swizzle](Swizzle-Node)
    * [Input](Input-Nodes)
        * Basic
            * [Boolean](Boolean-Node)
            * [Color](Color-Node)
            * [Constant](Constant-Node)
            * [Integer](Integer-Node)
            * [Slider](Slider-Node)
            * [Time](Time-Node)
            * [Float](Float)
            * [Vector 2](Vector-2-Node)
            * [Vector 3](Vector-3-Node)
            * [Vector 4](Vector-4-Node)
        * Geometry
            * [Bitangent Vector](Bitangent-Vector-Node)
            * [Instance ID](Instance-ID-Node)
            * [Normal Vector](Normal-Vector-Node)
            * [Position](Position-Node)
            * [Screen Position](Screen-Position-Node)
            * [Tangent Vector](Tangent-Vector-Node)
            * [UV](UV-Node)
            * [Vertex Color](Vertex-Color-Node)
            * [Vertex ID](Vertex-ID-Node)
            * [View Direction](View-Direction-Node)
            * [View Vector](View-Vector-Node)
        * Gradient
            * [Blackbody](Blackbody-Node)
            * [Gradient](Gradient-Node)
            * [Sample Gradient](Sample-Gradient-Node)
        * High Definition Render Pipeline
            * [Custom Color Buffer](HD-Custom-Color-Node.md)
            * [Custom Depth Buffer](HD-Custom-Depth-Node.md)
            * [Diffusion Profile](Diffusion-Profile-Node.md)
            * [Exposure](Exposure-Node.md)
            * [HD Scene Color](HD-Scene-Color-Node.md)
            * [HD Scene Depth](HD-Scene-Depth-Node.md)
            * [HD Sample Buffer](HD-Sample-Buffer-Node.md)
        * Lighting
            * [Ambient](Ambient-Node)
            * [Baked GI](Baked-GI-Node)
            * [Main Light Direction](https://docs.unity3d.com/Packages/com.unity.shadergraph@13.1/manual/Main-Light-Direction-Node.html)
            * [Reflection Probe](Reflection-Probe-Node)
        * Matrix
            * [Matrix 2x2](Matrix-2x2-Node)
            * [Matrix 3x3](Matrix-3x3-Node)
            * [Matrix 4x4](Matrix-4x4-Node)
            * [Transformation Matrix](Transformation-Matrix-Node)
        * Mesh Deformation
          * [Compute Deformation](Compute-Deformation-Node)
          * [Linear Blend Skinning](Linear-Blend-Skinning-Node)
        * PBR
            * [Dielectric Specular](Dielectric-Specular-Node)
            * [Metal Reflectance](Metal-Reflectance-Node)
            * [Fresnel Equation](Fresnel-Equation-Node)
        * Scene
            * [Camera](Camera-Node)
            * [Eye Index](Eye-Index-Node)
            * [Fog](Fog-Node)
            * [Object](Object-Node)
            * [Scene Color](Scene-Color-Node)
            * [Scene Depth](Scene-Depth-Node)
            * [Scene Depth Difference](Scene-Depth-Difference-Node)
            * [Screen](Screen-Node)
        * Texture
            * [Calculate Level Of Detail Texture 2D Node](Calculate-Level-Of-Detail-Texture-2D-Node)
            * [Cubemap Asset](Cubemap-Asset-Node)
            * [Gather Texture 2D Node](Gather-Texture-2D-Node)
            * [Sample Cubemap](Sample-Cubemap-Node)
            * [Sample Reflected Cubemap](Sample-Reflected-Cubemap-Node)
            * [Sample Texture 2D](Sample-Texture-2D-Node)
            * [Sample Texture 2D Array](Sample-Texture-2D-Array-Node)
            * [Sample Texture 2D LOD](Sample-Texture-2D-LOD-Node)
            * [Sample Texture 3D](Sample-Texture-3D-Node)
            * [Sample Virtual Texture](Sample-Virtual-Texture-Node)
            * [Sampler State](Sampler-State-Node)
            * [Split Texture Transform](Split-Texture-Transform-Node)
            * [Texture 2D Array Asset](Texture-2D-Array-Asset-Node)
            * [Texture 2D Asset](Texture-2D-Asset-Node)
            * [Texture 3D Asset](Texture-3D-Asset-Node)
            * [Texture Size](Texture-Size-Node)
    * [Math](Math-Nodes)
        * Advanced
            * [Absolute](Absolute-Node)
            * [Exponential](Exponential-Node)
            * [Length](Length-Node)
            * [Log](Log-Node)
            * [Modulo](Modulo-Node)
            * [Negate](Negate-Node)
            * [Normalize](Normalize-Node)
            * [Posterize](Posterize-Node)
            * [Reciprocal](Reciprocal-Node)
            * [Reciprocal Square Root](Reciprocal-Square-Root-Node)
        * Basic
            * [Add](Add-Node)
            * [Divide](Divide-Node)
            * [Multiply](Multiply-Node)
            * [Power](Power-Node)
            * [Square Root](Square-Root-Node)
            * [Subtract](Subtract-Node)
        * Derivative
            * [DDX](DDX-Node)
            * [DDXY](DDXY-Node)
            * [DDY](DDY-Node)
        * Interpolation
            * [Inverse Lerp](Inverse-Lerp-Node)
            * [Lerp](Lerp-Node)
            * [Smoothstep](Smoothstep-Node)
        * Matrix
            * [Matrix Construction](Matrix-Construction-Node)
            * [Matrix Determinant](Matrix-Determinant-Node)
            * [Matrix Split](Matrix-Split-Node)
            * [Matrix Transpose](Matrix-Transpose-Node)
        * Range
            * [Clamp](Clamp-Node)
            * [Fraction](Fraction-Node)
            * [Maximum](Maximum-Node)
            * [Minimum](Minimum-Node)
            * [One Minus](One-Minus-Node)
            * [Random Range](Random-Range-Node)
            * [Remap](Remap-Node)
            * [Saturate](Saturate-Node)
        * Round
            * [Ceiling](Ceiling-Node)
            * [Floor](Floor-Node)
            * [Round](Round-Node)
            * [Sign](Sign-Node)
            * [Step](Step-Node)
            * [Truncate](Truncate-Node)
        * Trigonometry
            * [Arccosine](Arccosine-Node)
            * [Arcsine](Arcsine-Node)
            * [Arctangent](Arctangent-Node)
            * [Arctangent2](Arctangent2-Node)
            * [Cosine](Cosine-Node)
            * [Degrees To Radians](Degrees-To-Radians-Node)
            * [Hyperbolic Cosine](Hyperbolic-Cosine-Node)
            * [Hyperbolic Sine](Hyperbolic-Sine-Node)
            * [Hyperbolic Tangent](Hyperbolic-Tangent-Node)
            * [Radians To Degrees](Radians-To-Degrees-Node)
            * [Sine](Sine-Node)
            * [Tangent](Tangent-Node)
        * Vector
            * [Cross Product](Cross-Product-Node)
            * [Distance](Distance-Node)
            * [Dot Product](Dot-Product-Node)
            * [Fresnel Effect](Fresnel-Effect-Node)
            * [Projection](Projection-Node)
            * [Reflection](Reflection-Node)
            * [Refract](Refract-Node)
            * [Rejection](Rejection-Node)
            * [Rotate About Axis](Rotate-About-Axis-Node)
            * [Sphere Mask](Sphere-Mask-Node)
            * [Transform](Transform-Node)
        * Wave
            * [Noise Sine Wave](Noise-Sine-Wave-Node)
            * [Sawtooth Wave](Sawtooth-Wave-Node)
            * [Square Wave](Square-Wave-Node)
            * [Triangle Wave](Triangle-Wave-Node)
    * [Procedural](Procedural-Nodes)
        * Noise
            * [Gradient Noise](Gradient-Noise-Node)
            * [Simple Noise](Simple-Noise-Node)
            * [Voronoi](Voronoi-Node)
        * Shapes
            * [Ellipse](Ellipse-Node)
            * [Polygon](Polygon-Node)
            * [Rectangle](Rectangle-Node)
            * [Rounded Polygon](Rounded-Polygon-Node)
            * [Rounded Rectangle](Rounded-Rectangle-Node)
        * [Checkerboard](Checkerboard-Node)
    * [Utility](Utility-Nodes)
        * Logic
            * [All](All-Node)
            * [And](And-Node)
            * [Any](Any-Node)
            * [Branch](Branch-Node)
            * [Branch On Input Connection](Branch-On-Input-Connection-Node.md)
            * [Comparison](Comparison-Node)
            * [Is Front Face](Is-Front-Face-Node)
            * [Is Infinite](Is-Infinite-Node)
            * [Is NaN](Is-NaN-Node)
            * [Nand](Nand-Node)
            * [Not](Not-Node)
            * [Or](Or-Node)
        * High Definition Render Pipeline
            * [Emission](Emission-Node.md)
            * Eye
              * [CirclePupilAnimation](Circle-Pupil-Animation-Node.md)
              * [CorneaRefraction](Cornea-Refraction-Node.md)
              * [EyeSurfaceTypeDebug](Eye-Surface-Type-Debug-Node.md)
              * [IrisLimbalRing](Iris-Limbal-Ring-Node.md)
              * [IrisOffset](Iris-Offset-Node.md)
              * [IrisOutOfBoundColorClamp](Iris-Out-Of-Bound-Color-Clamp-Node.md)
              * [IrisUVLocation](Iris-UV-Location-Node.md)
              * [ScleraIrisBlend](Sclera-Iris-Blend-Node.md)
              * [ScleraLimbalRing](Sclera-Limbal-Ring-Node.md)
              * [ScleraUVLocation](Sclera-UV-Location-Node.md)
            * Water
              * [Compute Vertex Position](Compute-Vertex-Position-Water-Node.md)
              * [Evaluate Foam Data](Evaluate-Foam-Data-Water-Node.md)
              * [Evaluate Refraction Data](Evaluate-Refraction-Data-Water-Node.md)
              * [Evaluate Scattering Color](Evaluate-Scattering-Color-Water-Node.md)
              * [Evaluate Simulation Additional Data](Evaluate-Simulation-Additional-Data-Water-Node.md)
              * [Evaluate Simulation Caustics](Evaluate-Simulation-Caustics-Water-Node.md)
              * [Evaluate Simulation Displacement](Evaluate-Simulation-Displacement-Water-Node.md)
              * [Evaluate Tip Thickness](Evaluate-Tip-Thickness-Water-Node.md)
              * [Pack Water Vertex Data](Pack-Vertex-Data-Water-Node.md)
              * [Unpack Water Data](Unpack-Data-Water-Node.md)              
            * Fabric
                *[ThreadMapDetail](ThreadMapDetail-Node.md)
            * [UVCombine](UVCombine-Node.md)
        * [Custom Function](Custom-Function-Node)
        * [Keyword](Keyword-Node)
        * [Preview](Preview-Node)
        * [Subgraph](Sub-graph-Node)
        * [Subgraph Dropdown node](Sub-Graph-Dropdown-Node.md)
    * [UV](UV-Nodes)
        * [Flipbook](Flipbook-Node)
        * [Polar Coordinates](Polar-Coordinates-Node)
        * [Radial Shear](Radial-Shear-Node)
        * [Rotate](Rotate-Node)
        * [Spherize](Spherize-Node)
        * [Tiling And Offset](Tiling-And-Offset-Node)
        * [Triplanar](Triplanar-Node)
        * [Twirl](Twirl-Node)
        * [Parallax Mapping](Parallax-Mapping-Node)
        * [Parallax Occlusion Mapping](Parallax-Occlusion-Mapping-Node)
    * [Block Nodes](Block-Node)
      * [Built In Blocks](Built-In-Blocks)
